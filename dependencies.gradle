ext {
  springBootVersion = "3.4.4" //Check spring overrides below when updating
  spotlessVersion = "7.1.0"
  jibVersion = "3.4.5"
  dependencyCheckVersion = "12.1.3"
  dependencyManagementVersion = "1.1.7"
  mongockVersion = "5.5.1"
  hazelcastVersion = "5.4.0"
  springDocVersion = "2.8.9"
  jedisVersion = "5.2.0"
  sentrySdkVersion = "6.15.0"
  awsSdkVersion = "2.31.77"
  streamExVersion = "0.8.3"
  ejmlVersion = "0.44.0"
  commonsLang3Version = "3.14.0"
  commonsCollections4Version = "4.5.0"
  commonsBeanUtilsVersion = "1.11.0"
  guavaVersion = "33.4.8-jre"
  fugueVersion = "6.1.2"
  queryDslVersion = "5.0.0"
  jetbrainsAnnotationsVersion = "26.0.2"
  lombokVersion = "1.18.38"
  mapstructVersion = "1.6.3"
  lombokMapstructBindingVersion = "0.2.0"
  logstashVersion = "7.0"
  groovyJsonVersion = "4.0.27"
  protostuffVersion = "1.8.0"
  spockVersion = "2.4-M6-groovy-4.0"
  sonarVersion = "6.2.0.5505"
  license3j = "3.3.0"
  tikaCoreVersion = "3.2.1"
  reflectionsVersions = "0.10.2"
  findBugsVersion = "3.0.2"
  easyRulesVersion = "4.1.0"
  strataVersion = "2.12.45-xplain-2"
  pyroscopeVersion = "2.1.2"
  tomcatVersion = "10.1.42"
}

ext['commons-lang3.version'] = "${commonsLang3Version}"
ext['spring-security.version'] = "6.4.6" // CVE-2025-22233 & CVE-2025-22234
ext['spring-framework.version'] = "6.2.8" // CVE-2025-41232 & CVE-2025-41234
ext['kafka.version'] = "3.9.1" // CVE-2025-27817 & CVE-2025-27818
ext['netty.version'] = "4.1.119.Final"
ext['tomcat.version'] = "${tomcatVersion}"
ext['jedis.version'] = "${jedisVersion}"


