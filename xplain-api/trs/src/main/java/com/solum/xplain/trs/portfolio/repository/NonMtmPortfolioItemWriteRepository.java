package com.solum.xplain.trs.portfolio.repository;

import static java.util.stream.Collectors.groupingBy;
import static org.springframework.data.mongodb.core.query.Criteria.where;

import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.value.ArchiveEntityForm;
import com.solum.xplain.core.common.versions.embedded.EmbeddedVersionEntityMongoOperations;
import com.solum.xplain.core.common.versions.embedded.EmbeddedVersionEntityWriteRepository;
import com.solum.xplain.core.common.versions.embedded.update.EntityForUpdate;
import com.solum.xplain.core.common.versions.embedded.update.EntityUpdateOperations;
import com.solum.xplain.core.common.versions.embedded.update.EntityUpdateResult;
import com.solum.xplain.core.common.versions.embedded.update.ImportUpdatesResolver;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.users.AuditUser;
import com.solum.xplain.trs.portfolio.NonMtmPortfolioItem;
import com.solum.xplain.trs.portfolio.NonMtmPortfolioItemEntity;
import com.solum.xplain.trs.portfolio.event.NonMtmPortfolioItemsStatesUpdated;
import com.solum.xplain.trs.portfolio.event.NonMtmPortfolioUpdated;
import com.solum.xplain.trs.portfolio.trade.NonMtmPortfolioItemEntityToViewConverterProvider;
import com.solum.xplain.trs.portfolio.trade.TrsTradeMapper;
import com.solum.xplain.trs.portfolio.trade.form.TrsTradeForm;
import com.solum.xplain.trs.portfolio.trade.value.TrsTradeValue;
import com.solum.xplain.trs.portfolio.value.NonMtmPortfolioItemUniqueKey;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;
import org.jspecify.annotations.NullMarked;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.AuditorAware;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Repository;

@Repository
@NullMarked
public class NonMtmPortfolioItemWriteRepository
    implements EmbeddedVersionEntityWriteRepository<TrsTradeValue, NonMtmPortfolioItemEntity> {

  private final ApplicationEventPublisher eventPublisher;
  private final EmbeddedVersionEntityMongoOperations<
          TrsTradeValue, NonMtmPortfolioItemEntity, NonMtmPortfolioItem>
      operations;
  private final TrsTradeMapper trsTradeMapper;

  public NonMtmPortfolioItemWriteRepository(
      ApplicationEventPublisher eventPublisher,
      MongoOperations mongoOperations,
      AuditorAware<AuditUser> auditUserAuditorAware,
      TrsTradeMapper trsTradeMapper,
      NonMtmPortfolioItemEntityToViewConverterProvider converterProvider) {
    this.eventPublisher = eventPublisher;
    this.trsTradeMapper = trsTradeMapper;
    this.operations =
        new EmbeddedVersionEntityMongoOperations<>(
            NonMtmPortfolioItemEntity.class,
            NonMtmPortfolioItem.class,
            mongoOperations,
            converterProvider,
            new EntityUpdateOperations<>(auditUserAuditorAware),
            this::processUpdates);
  }

  private void processUpdates(
      Map<EntityUpdateResult<TrsTradeValue, NonMtmPortfolioItemEntity>, List<NonMtmPortfolioItem>>
          items) {
    items.entrySet().stream()
        .filter(i -> i.getKey().isNew() || i.getKey().isRemoved())
        .flatMap(i -> i.getValue().stream())
        .collect(groupingBy(NonMtmPortfolioItem::getPortfolioId))
        .entrySet()
        .stream()
        .map(
            i ->
                NonMtmPortfolioItemsStatesUpdated.builder()
                    .nonMtmPortfolioId(i.getKey())
                    .items(i.getValue())
                    .build())
        .forEach(eventPublisher::publishEvent);
  }

  private EntityId publishUpdateEvent(String nonMtmPortfolioId, EntityId tradeId) {
    eventPublisher.publishEvent(new NonMtmPortfolioUpdated(nonMtmPortfolioId));
    return tradeId;
  }

  private Criteria entityCriteria(String nonMtmPortfolioId, String tradeEntityId) {
    return where(NonMtmPortfolioItemEntity.Fields.id)
        .is(tradeEntityId)
        .and(NonMtmPortfolioItemEntity.Fields.nonMtmPortfolioId)
        .is(nonMtmPortfolioId);
  }

  public Either<ErrorItem, EntityId> insert(String nonMtmPortfolioId, TrsTradeForm form) {
    var key = NonMtmPortfolioItemUniqueKey.newOf(nonMtmPortfolioId, form.getExternalTradeId());
    var newDefault = new NonMtmPortfolioItemEntity(key);
    var trsTradeValue = trsTradeMapper.from(form);
    var tradeId = operations.insert(trsTradeValue, newDefault, form.getVersionForm());
    return Either.right(publishUpdateEvent(nonMtmPortfolioId, tradeId));
  }

  public Either<ErrorItem, EntityId> update(
      String nonMtmPortfolioId, String tradeEntityId, LocalDate version, TrsTradeForm form) {
    var criteria = entityCriteria(nonMtmPortfolioId, tradeEntityId);
    var trsTradeValue = trsTradeMapper.from(form);
    return operations
        .update(criteria, version, trsTradeValue, form.getVersionForm())
        .map(tradeId -> publishUpdateEvent(nonMtmPortfolioId, tradeId));
  }

  public Either<ErrorItem, EntityId> archiveItem(
      String nonMtmPortfolioId,
      String tradeEntityId,
      LocalDate version,
      ArchiveEntityForm archiveEntityForm) {
    var criteria = entityCriteria(nonMtmPortfolioId, tradeEntityId);
    return operations
        .archive(criteria, version, archiveEntityForm)
        .map(tradeId -> publishUpdateEvent(nonMtmPortfolioId, tradeId));
  }

  public Either<ErrorItem, EntityId> deleteItem(
      String nonMtmPortfolioId, String tradeEntityId, LocalDate version) {
    var criteria = entityCriteria(nonMtmPortfolioId, tradeEntityId);
    return operations
        .deleteItem(criteria, version)
        .map(tradeId -> publishUpdateEvent(nonMtmPortfolioId, tradeId));
  }

  public Stream<EntityForUpdate<TrsTradeValue, NonMtmPortfolioItemEntity>> streamEntitiesForUpdate(
      Collection<String> nonMtmPortfolioIds, LocalDate stateDate) {
    return operations.streamValidEntitiesForUpdate(
        where(NonMtmPortfolioItemEntity.Fields.nonMtmPortfolioId).in(nonMtmPortfolioIds),
        stateDate);
  }

  public Integer updateFromImport(
      ImportUpdatesResolver<TrsTradeValue, NonMtmPortfolioItemEntity> updateActions) {
    return operations.updateFromImport(updateActions);
  }
}
