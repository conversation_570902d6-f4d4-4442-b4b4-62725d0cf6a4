package com.solum.xplain.trs.portfolio.csv;

import static com.solum.xplain.core.common.CollectionUtils.chunked;
import static com.solum.xplain.core.common.CollectionUtils.toSizedList;
import static com.solum.xplain.core.common.csv.ImportDescriptionUtils.description;
import static com.solum.xplain.core.common.csv.ImportErrorUtils.stateChanged;
import static com.solum.xplain.core.common.csv.LargeImportUtils.validateDuplicateItems;
import static com.solum.xplain.core.common.csv.LargeImportUtils.validateExistingItemFutureVersions;
import static com.solum.xplain.core.common.csv.LargeImportUtils.validateMissingItemFutureVersions;
import static com.solum.xplain.core.common.csv.LargeImportUtils.validateMissingItems;
import static com.solum.xplain.core.common.csv.LargeImportUtils.validateViableNewVersions;
import static com.solum.xplain.trs.portfolio.value.NonMtmPortfolioFilter.activeNonMtmPortfolios;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toSet;

import com.solum.xplain.core.audit.AuditEntryService;
import com.solum.xplain.core.audit.entity.AuditEntry;
import com.solum.xplain.core.authentication.AuthenticationContext;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.csv.CompositeVersionedImportItems;
import com.solum.xplain.core.common.csv.CsvLoader;
import com.solum.xplain.core.common.csv.CsvParserResult;
import com.solum.xplain.core.common.csv.ImportActionSummary;
import com.solum.xplain.core.common.csv.ImportOptions;
import com.solum.xplain.core.common.csv.LargeVersionedImportItems;
import com.solum.xplain.core.common.csv.ParsingMode;
import com.solum.xplain.core.common.csv.VersionedImportItems;
import com.solum.xplain.core.common.team.UserTeamEntity;
import com.solum.xplain.core.common.versions.embedded.ResolvableEmbeddedVersionValue;
import com.solum.xplain.core.common.versions.embedded.update.EntityForUpdate;
import com.solum.xplain.core.csv.LargeFileImporter;
import com.solum.xplain.core.csv.ValidationResponse;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.error.ErrorItem.ListOfErrors;
import com.solum.xplain.trs.portfolio.NonMtmPortfolioItemEntity;
import com.solum.xplain.trs.portfolio.NonMtmPortfolioTeamFilter;
import com.solum.xplain.trs.portfolio.NonMtmPortfolioTeamFilterProvider;
import com.solum.xplain.trs.portfolio.csv.form.NonMtmPortfolioItemCsvForm;
import com.solum.xplain.trs.portfolio.event.NonMtmPortfolioUpdated;
import com.solum.xplain.trs.portfolio.repository.NonMtmPortfolioItemWriteRepository;
import com.solum.xplain.trs.portfolio.repository.NonMtmPortfolioRepository;
import com.solum.xplain.trs.portfolio.trade.value.TrsTradeValue;
import com.solum.xplain.trs.portfolio.value.NonMtmPortfolioItemUniqueKey;
import com.solum.xplain.trs.portfolio.value.NonMtmPortfolioView;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.jspecify.annotations.NullMarked;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;

@Service
@NullMarked
public class NonMtmPortfolioItemUploadService {

  private static final String OBJECT_NAME = "TrsTrade";
  private final AuthenticationContext authenticationContext;
  private final AuditEntryService auditEntryService;
  private final NonMtmPortfolioRepository nonMtmPortfolioRepository;
  private final NonMtmPortfolioItemWriteRepository nonMtmPortfolioItemWriteRepository;
  private final NonMtmPortfolioTeamFilterProvider nonMtmPortfolioTeamFilterProvider;
  private final TrsTradeCsvLoaderFactory trsTradeCsvLoaderFactory;
  private final ApplicationEventPublisher eventPublisher;

  public NonMtmPortfolioItemUploadService(
      AuthenticationContext authenticationContext,
      AuditEntryService auditEntryService,
      NonMtmPortfolioRepository nonMtmPortfolioRepository,
      NonMtmPortfolioItemWriteRepository nonMtmPortfolioItemWriteRepository,
      NonMtmPortfolioTeamFilterProvider nonMtmPortfolioTeamFilterProvider,
      TrsTradeCsvLoaderFactory trsTradeCsvLoaderFactory,
      ApplicationEventPublisher eventPublisher) {
    this.authenticationContext = authenticationContext;
    this.auditEntryService = auditEntryService;
    this.nonMtmPortfolioRepository = nonMtmPortfolioRepository;
    this.nonMtmPortfolioItemWriteRepository = nonMtmPortfolioItemWriteRepository;
    this.nonMtmPortfolioTeamFilterProvider = nonMtmPortfolioTeamFilterProvider;
    this.trsTradeCsvLoaderFactory = trsTradeCsvLoaderFactory;
    this.eventPublisher = eventPublisher;
  }

  public Either<List<ErrorItem>, ValidationResponse> validate(
      CsvLoader<NonMtmPortfolioItemCsvForm> loader, byte[] bytes, ImportOptions importOptions) {
    return loadCsv(loader, importOptions.parsingMode(), bytes)
        .flatMap(f -> validateFile(f, importOptions.getStateDate()).leftMap(ListOfErrors::from));
  }

  public Either<List<ErrorItem>, List<EntityId>> importTrades(
      CsvLoader<NonMtmPortfolioItemCsvForm> loader, byte[] bytes, ImportOptions importOptions) {
    return loadCsv(loader, importOptions.parsingMode(), bytes)
        .flatMap(f -> importTrades(f, importOptions).leftMap(ListOfErrors::from));
  }

  private Either<ErrorItem, ValidationResponse> validateFile(
      CsvParserResult<NonMtmPortfolioItemCsvForm> parserResult, LocalDate stateDate) {
    var importItems = groupedByPortfolioImportItems(parserResult.getParsedLines(), stateDate);
    return importItems.stream()
        .map(group -> validateItemsGroup(group, stateDate))
        .flatMap(Collection::stream)
        .collect(collectingAndThen(toList(), l -> Either.right(new ValidationResponse(l))));
  }

  public Either<List<ErrorItem>, List<EntityId>> uploadTrades(
      Authentication user, byte[] file, ImportOptions importOptions) {
    return performWithNonMtmPortfolioFilter(
            user,
            filter ->
                nonMtmPortfolioRepository.portfolioCondensedViews(activeNonMtmPortfolios(), filter))
        .leftMap(ErrorItem.ListOfErrors::from)
        .map(trsTradeCsvLoaderFactory::standard)
        .flatMap(l -> importTrades(l, file, importOptions));
  }

  public Either<List<ErrorItem>, ValidationResponse> validateTradesImport(
      Authentication user, byte[] file, ImportOptions importOptions) {
    return performWithNonMtmPortfolioFilter(
            user,
            filter ->
                nonMtmPortfolioRepository.portfolioCondensedViews(activeNonMtmPortfolios(), filter))
        .leftMap(ErrorItem.ListOfErrors::from)
        .map(trsTradeCsvLoaderFactory::standard)
        .flatMap(l -> validate(l, file, importOptions));
  }

  public Either<List<ErrorItem>, List<EntityId>> uploadTradesForPortfolio(
      Authentication user, String portfolioId, byte[] file, ImportOptions importOptions) {
    return getUserPortfolio(user, portfolioId)
        .leftMap(ErrorItem.ListOfErrors::from)
        .map(UserTeamEntity::getView)
        .map(trsTradeCsvLoaderFactory::forPortfolio)
        .flatMap(l -> importTrades(l, file, importOptions));
  }

  public Either<List<ErrorItem>, ValidationResponse> validateUploadForPortfolio(
      Authentication user, String portfolioId, byte[] file, ImportOptions importOptions) {
    return getUserPortfolio(user, portfolioId)
        .leftMap(ErrorItem.ListOfErrors::from)
        .map(UserTeamEntity::getView)
        .map(trsTradeCsvLoaderFactory::forPortfolio)
        .flatMap(l -> validate(l, file, importOptions));
  }

  private <T> Either<ErrorItem, T> performWithNonMtmPortfolioFilter(
      Authentication auth, Function<NonMtmPortfolioTeamFilter, T> fetchFunction) {
    return authenticationContext
        .userEither(auth)
        .map(nonMtmPortfolioTeamFilterProvider::provideFilter)
        .map(fetchFunction);
  }

  private Either<ErrorItem, UserTeamEntity<NonMtmPortfolioView>> getUserPortfolio(
      Authentication user, String portfolioId) {
    return authenticationContext
        .userEither(user)
        .flatMap(u -> nonMtmPortfolioRepository.getUserPortfolioView(u, portfolioId));
  }

  private List<ErrorItem> validateItemsGroup(
      VersionedImportItems<TrsTradeValue, NonMtmPortfolioItemUniqueKey, NonMtmPortfolioItemEntity>
          importItems,
      LocalDate stateDate) {
    final List<ErrorItem> errors = new ArrayList<>();
    validateExistingItemFutureVersions(importItems, stateDate).ifPresent(errors::add);
    validateMissingItemFutureVersions(importItems, stateDate).ifPresent(errors::add);
    validateViableNewVersions(importItems, stateDate).ifPresent(errors::add);
    validateDuplicateItems(importItems.getDuplicateActiveItemsKeys()).ifPresent(errors::add);
    validateMissingItems(importItems.getSpareItemsKeys()).ifPresent(errors::add);
    return errors;
  }

  private static VersionedImportItems<
          TrsTradeValue, NonMtmPortfolioItemUniqueKey, NonMtmPortfolioItemEntity>
      buildVersionItems(
          List<NonMtmPortfolioItemCsvForm> forms,
          List<EntityForUpdate<TrsTradeValue, NonMtmPortfolioItemEntity>> existingItems) {
    return LargeVersionedImportItems
        .<TrsTradeValue, NonMtmPortfolioItemUniqueKey, NonMtmPortfolioItemEntity>builder()
        .existingItems(existingItems)
        .existingItemToKeyFn(e -> e.getEntity().uniqueKey())
        .importItems(List.copyOf(forms))
        .importItemToKeyFn(ResolvableEmbeddedVersionValue::getEntityId)
        .newEntityFn(NonMtmPortfolioItemEntity::new)
        .build();
  }

  private Either<ErrorItem, List<EntityId>> importTrades(
      CsvParserResult<NonMtmPortfolioItemCsvForm> parserResult, ImportOptions importOptions) {
    var forms = parserResult.getParsedLines();
    var portfolioIds =
        forms.stream().map(e -> e.getUniqueKey().getNonMtmPortfolioId()).collect(toSet());
    if (nonMtmPortfolioRepository.hasChanges(
        portfolioIds, importOptions.getValidationTimestamp())) {
      return Either.left(stateChanged("Non-Mtm Portfolios"));
    }
    var stateDate = importOptions.getStateDate();
    List<
            VersionedImportItems<
                TrsTradeValue, NonMtmPortfolioItemUniqueKey, NonMtmPortfolioItemEntity>>
        importItemsGroups = groupedByPortfolioImportItems(forms, stateDate);
    var importer =
        new LargeFileImporter<
            TrsTradeValue, NonMtmPortfolioItemUniqueKey, NonMtmPortfolioItemEntity>(
            importOptions, nonMtmPortfolioItemWriteRepository);
    var results =
        chunked(importItemsGroups.stream())
            .map(
                importItems -> {
                  var composite = new CompositeVersionedImportItems<>(importItems);
                  return new LargeFileImporter<
                          TrsTradeValue, NonMtmPortfolioItemUniqueKey, NonMtmPortfolioItemEntity>(
                          importOptions, nonMtmPortfolioItemWriteRepository)
                      .importItems(composite);
                })
            .flatMap(Collection::stream)
            .toList();
    logImport(results, parserResult.getWarnings());
    markPortfolioUpdate(portfolioIds);
    return portfolioIds.stream()
        .map(EntityId::new)
        .collect(Collectors.collectingAndThen(toList(), Either::right));
  }

  private void markPortfolioUpdate(Set<String> portfolioIds) {
    LocalDateTime importDate = LocalDateTime.now();
    portfolioIds.stream()
        .map(p -> new NonMtmPortfolioUpdated(p, importDate))
        .forEach(eventPublisher::publishEvent);
  }

  private void logImport(List<ImportActionSummary> summaries, List<ErrorItem> warnings) {
    var changesCount = summaries.stream().mapToInt(ImportActionSummary::getChangesCount).sum();
    var description = description(OBJECT_NAME, 0, warnings.size(), changesCount);
    auditEntryService.newEntryWithLogs(AuditEntry.of("nonMtmPortoflioItem", description), warnings);
  }

  private Either<List<ErrorItem>, CsvParserResult<NonMtmPortfolioItemCsvForm>> loadCsv(
      CsvLoader<NonMtmPortfolioItemCsvForm> loader, ParsingMode parsingMode, byte[] bytes) {
    return loader.parse(bytes, parsingMode);
  }

  private List<
          VersionedImportItems<
              TrsTradeValue, NonMtmPortfolioItemUniqueKey, NonMtmPortfolioItemEntity>>
      groupedByPortfolioImportItems(List<NonMtmPortfolioItemCsvForm> forms, LocalDate stateDate) {
    var byPortfolioIds =
        forms.stream().collect(groupingBy(f -> f.getUniqueKey().getNonMtmPortfolioId()));

    // These are estimates to avoid excessive resizing of collections during grouping.
    var estimatedMapSize = byPortfolioIds.size();
    var estimatedItemPerPortfolio =
        byPortfolioIds.values().stream().mapToInt(List::size).max().orElse(10);

    var allEntitiesForUpdate =
        nonMtmPortfolioItemWriteRepository
            .streamEntitiesForUpdate(byPortfolioIds.keySet(), stateDate)
            .collect(
                groupingBy(
                    item -> item.getEntity().getNonMtmPortfolioId(),
                    () -> new HashMap<>(estimatedMapSize),
                    toSizedList(estimatedItemPerPortfolio)));

    return byPortfolioIds.entrySet().stream()
        .map(
            byPortfolioId -> {
              var entitiesForUpdate = allEntitiesForUpdate.get(byPortfolioId.getKey());
              return buildVersionItems(byPortfolioId.getValue(), entitiesForUpdate);
            })
        .toList();
  }
}
