package com.solum.xplain.trs.portfolio.csv.form;

import com.solum.xplain.core.common.versions.embedded.ResolvableEmbeddedVersionValue;
import com.solum.xplain.trs.portfolio.trade.value.TrsTradeValue;
import com.solum.xplain.trs.portfolio.value.NonMtmPortfolioItemUniqueKey;

public class NonMtmPortfolioItemCsvForm
    implements ResolvableEmbeddedVersionValue<TrsTradeValue, NonMtmPortfolioItemUniqueKey> {

  private final NonMtmPortfolioItemUniqueKey uniqueKey;
  private final TrsTradeValue value;

  public NonMtmPortfolioItemCsvForm(NonMtmPortfolioItemUniqueKey uniqueKey, TrsTradeValue value) {
    this.uniqueKey = uniqueKey;
    this.value = value;
  }

  public static NonMtmPortfolioItemCsvForm newOf(
      String nonMtmPortfolioId, String externalTrsTradeId, TrsTradeValue value) {
    return new NonMtmPortfolioItemCsvForm(
        NonMtmPortfolioItemUniqueKey.newOf(nonMtmPortfolioId, externalTrsTradeId), value);
  }

  public NonMtmPortfolioItemUniqueKey getUniqueKey() {
    return uniqueKey;
  }

  @Override
  public TrsTradeValue toVersionValue() {
    return value;
  }

  @Override
  public NonMtmPortfolioItemUniqueKey getEntityId() {
    return getUniqueKey();
  }
}
