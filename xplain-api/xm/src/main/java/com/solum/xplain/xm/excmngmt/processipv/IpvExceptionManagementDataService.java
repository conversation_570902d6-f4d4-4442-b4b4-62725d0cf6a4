package com.solum.xplain.xm.excmngmt.processipv;

import static com.solum.xplain.core.error.Error.OBJECT_NOT_FOUND;
import static java.lang.String.format;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toList;

import com.solum.xplain.core.calculationapi.CalculationEventProducer;
import com.solum.xplain.core.calculationapi.CalculationResultProvider;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.ipv.data.IpvDataRepository;
import com.solum.xplain.core.ipv.data.IpvDataType;
import com.solum.xplain.core.ipv.data.form.IpvDataProviderValueFilter;
import com.solum.xplain.core.portfolio.value.PortfolioItemCalculatedExcMngmntView;
import com.solum.xplain.xm.dashboards.events.DashboardDeletedEvent;
import com.solum.xplain.xm.dashboards.repository.DashboardRepository;
import com.solum.xplain.xm.dashboards.views.DashboardDateView;
import com.solum.xplain.xm.excmngmt.enums.IpvExceptionManagementPhase;
import com.solum.xplain.xm.excmngmt.processipv.value.IpvExceptionManagementData;
import com.solum.xplain.xm.excmngmt.trade.ResolvedIpvTrade;
import com.solum.xplain.xm.settings.ExceptionManagementSettingsRepository;
import com.solum.xplain.xm.settings.IpvValueNavLevel;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class IpvExceptionManagementDataService {

  private final IpvExceptionManagementCalculationRepository
      exceptionManagementCalculationRepository;
  private final CalculationResultProvider calculationResultProvider;
  private final IpvDataRepository dataRepository;
  private final DashboardRepository dashboardRepository;
  private final CalculationEventProducer calculationEventProducer;
  private final ExceptionManagementSettingsRepository exceptionManagementSettingsRepository;

  public IpvExceptionManagementData providerData(String ipvDataGroupId, LocalDate date) {
    IpvValueNavLevel navLevel =
        exceptionManagementSettingsRepository.getNavLevelEnum(BitemporalDate.newOfNow());

    if (IpvValueNavLevel.ENTITY_LEVEL.equals(navLevel)) {
      // combine NAV and EXCLUDE NAV data
      return Stream.concat(
              dataRepository.getValueViewsStream(
                  ipvDataGroupId,
                  new IpvDataProviderValueFilter(date, true, false),
                  IpvDataType.NAV),
              dataRepository.getValueViewsStream(
                  ipvDataGroupId,
                  new IpvDataProviderValueFilter(date, true, false),
                  IpvDataType.EXCLUDE_NAV))
          .collect(
              collectingAndThen(toList(), data -> IpvExceptionManagementData.of(data, navLevel)));
    }
    return dataRepository
        .getValueViewsStream(
            ipvDataGroupId, IpvDataProviderValueFilter.newOfValuationDate(date), IpvDataType.ALL)
        .collect(
            collectingAndThen(toList(), data -> IpvExceptionManagementData.of(data, navLevel)));
  }

  /**
   * Convenience method combining two other methods. Finds the {@link #previousDashboard(String,
   * LocalDate)} from the given date and then gets the {@link
   * #historicalExceptionManagementData(Optional, String, String, LocalDate)} for that dashboard's
   * date.
   *
   * @param portfolioId the portfolio ID to return data for
   * @param ipvDataGroupId the valuation data group to return data for
   * @param date the valuation date of this dashboard, so we can find the previous one
   * @return the previous day's exception management data, or null if nothing could be found
   */
  public IpvExceptionManagementData previousDayExceptionManagementData(
      String portfolioId, String ipvDataGroupId, LocalDate date) {
    return previousDashboard(portfolioId, date)
        .map(v -> v.dateRange().singleDate())
        .map(
            previousDate ->
                historicalExceptionManagementData(
                    Optional.empty(), portfolioId, ipvDataGroupId, previousDate))
        .orElseGet(IpvExceptionManagementData::empty);
  }

  public Optional<DashboardDateView> previousDashboard(String portfolioId, LocalDate date) {
    return dashboardRepository.previousPortfolioDashboardId(portfolioId, date);
  }

  public IpvExceptionManagementData historicalExceptionManagementData(
      Optional<IpvExceptionManagementPhase> phase,
      String portfolioId,
      String ipvDataGroupId,
      LocalDate date) {
    IpvValueNavLevel navLevel =
        exceptionManagementSettingsRepository.getNavLevelEnum(BitemporalDate.newOfNow());
    return exceptionManagementCalculationRepository
        .getResolvedTradeData(phase, portfolioId, ipvDataGroupId, date)
        .stream()
        .map(ResolvedIpvTrade::toIpvDataProviderValue)
        .flatMap(Collection::stream)
        .collect(
            collectingAndThen(toList(), data -> IpvExceptionManagementData.of(data, navLevel)));
  }

  /**
   * Cleans up all calculation data for a deleted dashboard.
   *
   * @param event event fired when the dashboard is deleted
   */
  @EventListener
  void cleanupCalculationData(DashboardDeletedEvent event) {
    calculationResultProvider
        .latestDashboardCalculationsByPortfolioId(event.getDashboardId())
        .values()
        .stream()
        .filter(Objects::nonNull)
        .forEach(
            calculationId ->
                calculationEventProducer.publishDeleteEvent(calculationId.toHexString()));
  }

  /**
   * Convenience method combining two other methods. Finds the {@link #previousDashboard(String,
   * LocalDate)} from the given date and then gets the {@link #calculationData(String, String,
   * String, LocalDate, List)} for that dashboard's date.
   *
   * @param portfolioId the portfolio ID to return data for
   * @param marketDataGroupId the market data group to return calculation data for
   * @param externalPortfolioId the external ID of the portfolio, used for logging and error
   *     reporting
   * @param date the valuation date of this dashboard, so we can find the previous one
   * @param logs error log to accumulate
   * @return the previous day's calculation data
   */
  public List<PortfolioItemCalculatedExcMngmntView> previousDateCalculationData(
      String portfolioId,
      String marketDataGroupId,
      String externalPortfolioId,
      LocalDate date,
      List<ErrorItem> logs) {
    return previousDashboard(portfolioId, date)
        .map(v -> v.dateRange().singleDate())
        .map(
            previousDate ->
                calculationData(
                    portfolioId, marketDataGroupId, externalPortfolioId, previousDate, logs))
        .orElseGet(List::of);
  }

  /**
   * Gets the Xplain calculation data for a dashboard calculation.
   *
   * @param portfolioId the portfolio ID to return data for
   * @param marketDataGroupId the market data group to return calculation data for
   * @param externalPortfolioId the external ID of the portfolio, used for logging and error
   *     reporting
   * @param date the valuation date to return data for
   * @param logs error log to accumulate
   * @return the calculation data for the given date
   */
  public List<PortfolioItemCalculatedExcMngmntView> calculationData(
      String portfolioId,
      String marketDataGroupId,
      String externalPortfolioId,
      LocalDate date,
      List<ErrorItem> logs) {
    return calculationResultProvider
        .latestDashboardCalculation(portfolioId, date, marketDataGroupId)
        .map(Either::<ErrorItem, ObjectId>right)
        .orElseGet(
            () ->
                Either.left(
                    OBJECT_NOT_FOUND.entity(
                        format(
                            "Unable to find valid XPLAIN calculation for date %s portfolio %s",
                            date, externalPortfolioId))))
        .map(calculationResultProvider::calculationResultExceptionMngmntItems)
        .leftMap(logs::add)
        .toOptional()
        .orElseGet(List::of);
  }
}
