package com.solum.xplain.xm.excmngmt.processipv.value;

import static com.google.common.collect.ImmutableSet.toImmutableSet;
import static com.solum.xplain.core.providers.DataProvider.NAV_PROVIDER_CODE;
import static java.util.Optional.ofNullable;
import static java.util.function.Function.identity;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toMap;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Streams;
import com.solum.xplain.core.ipv.ValuationDataKeyPrefixUtils;
import com.solum.xplain.core.ipv.data.value.IpvDataProviderValueView;
import com.solum.xplain.xm.excmngmt.processipv.data.ProviderDataValue;
import com.solum.xplain.xm.excmngmt.processipv.data.ProviderDataWithGreeks;
import com.solum.xplain.xm.excmngmt.processipv.data.Trade;
import com.solum.xplain.xm.settings.IpvValueNavLevel;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;

@Slf4j
public record IpvExceptionManagementData(
    /** Map of VDK to (non-NAV) providers to data. */
    @NonNull Map<String, TradeBreakValuationData> valuationData,
    /** Map of VDK to NAV value. */
    @NonNull Map<String, BigDecimal> navData,
    /** Map of VDK to notional value. */
    @NonNull Map<String, BigDecimal> notionalData,
    /** Global nav level setting. */
    @NonNull IpvValueNavLevel navLevel) {

  public static IpvExceptionManagementData empty() {
    return new IpvExceptionManagementData(
        Collections.emptyMap(),
        Collections.emptyMap(),
        Collections.emptyMap(),
        IpvValueNavLevel.TRADE_LEVEL); // TRADE_LEVEL is the default navLevel
  }

  /**
   * This list of raw data is expected to contain corresponding NAV values for each VDK i.e. {@link
   * IpvDataProviderValueView#getNavValue()} is populated.
   */
  public static IpvExceptionManagementData of(
      @NonNull List<IpvDataProviderValueView> rawData, @NonNull IpvValueNavLevel navLevel) {
    // Map of VDK to (wrapped map of) providers to data. */
    var raw =
        rawData.stream()
            .filter(v -> !NAV_PROVIDER_CODE.equals(v.getProvider()))
            .collect(
                groupingBy(
                    IpvDataProviderValueView::getKey,
                    collectingAndThen(
                        toMap(IpvDataProviderValueView::getProvider, Function.identity()),
                        TradeBreakValuationData::new)));
    // Map of VDK to notional. */
    var notionalByVdk = notionalByVdk(rawData);
    // Map of VDK to NAV. */
    var navData =
        rawData.stream()
            .filter(v -> NAV_PROVIDER_CODE.equals(v.getProvider()))
            .collect(toMap(IpvDataProviderValueView::getKey, IpvDataProviderValueView::getValue));
    var scaling = navData.keySet().stream().distinct().collect(toMap(identity(), navData::get));
    return new IpvExceptionManagementData(raw, scaling, notionalByVdk, navLevel);
  }

  public BigDecimal getNavForTrade(@NonNull Trade trade) {
    String lookupKey =
        switch (navLevel) {
          case ENTITY_LEVEL ->
              ValuationDataKeyPrefixUtils.toValuationDataKeyPrefix(
                  trade.getExternalCompanyId(), trade.getExternalEntityId());
          case TRADE_LEVEL -> trade.getKey();
        };
    return navData.get(lookupKey);
  }

  private static Map<String, BigDecimal> notionalByVdk(List<IpvDataProviderValueView> rawData) {
    var notionalByVdk = new java.util.HashMap<String, BigDecimal>();
    rawData.stream()
        .filter(v -> v.getNotional() != null)
        .filter(v -> !NAV_PROVIDER_CODE.equals(v.getProvider()))
        .collect(groupingBy(IpvDataProviderValueView::getKey))
        .forEach(
            (vdk, value) ->
                value.stream()
                    .map(IpvDataProviderValueView::getNotional)
                    .findFirst()
                    .ifPresent(n -> notionalByVdk.put(vdk, n)));
    return notionalByVdk;
  }

  Map<String, List<ProviderDataWithGreeks>> combinedData(IpvExceptionManagementData prevData) {
    var allTrades =
        Streams.concat(valuationData.keySet().stream(), prevData.valuationData.keySet().stream())
            .collect(toImmutableSet());

    var results = ImmutableMap.<String, List<ProviderDataWithGreeks>>builder();
    for (var trade : allTrades) {
      var providerValues = combinedDataForTrade(prevData, trade);

      results.put(trade, providerValues);
    }
    return results.build();
  }

  public List<ProviderDataWithGreeks> combinedDataForTrade(
      IpvExceptionManagementData prevData, String tradeKey) {
    var currentData =
        ofNullable(valuationData.get(tradeKey)).orElseGet(TradeBreakValuationData::empty);
    var previousData =
        ofNullable(prevData.valuationData.get(tradeKey)).orElseGet(TradeBreakValuationData::empty);

    var allProviders =
        ImmutableSet.<String>builder()
            .addAll(currentData.uniqueProviders())
            .addAll(previousData.uniqueProviders())
            .build();

    var providerValues =
        allProviders.stream()
            .map(
                provider ->
                    toProviderData(
                        provider,
                        currentData.providerData(provider),
                        previousData.providerData(provider)))
            .toList();
    return providerValues;
  }

  public ProviderDataWithGreeks toProviderData(
      @NonNull String provider,
      @NonNull IpvDataProviderValueView currData,
      @NonNull IpvDataProviderValueView prevData) {
    var pd = new ProviderDataWithGreeks();
    pd.setProvider(provider);
    pd.setPv(new ProviderDataValue(currData.getValue(), prevData.getValue()));
    pd.setDelta(new ProviderDataValue(currData.getDelta(), prevData.getDelta()));
    pd.setVega(new ProviderDataValue(currData.getVega(), prevData.getVega()));
    pd.setGamma(new ProviderDataValue(currData.getGamma(), prevData.getGamma()));
    pd.setTheta(new ProviderDataValue(currData.getTheta(), prevData.getTheta()));
    pd.setRho(new ProviderDataValue(currData.getRho(), prevData.getRho()));
    pd.setParRate(new ProviderDataValue(currData.getParRate(), prevData.getParRate()));
    pd.setSpotRate(new ProviderDataValue(currData.getSpotRate(), prevData.getSpotRate()));
    pd.setImpliedVol(new ProviderDataValue(currData.getImpliedVol(), prevData.getImpliedVol()));
    pd.setAtmImpliedVol(
        new ProviderDataValue(currData.getAtmImpliedVol(), prevData.getAtmImpliedVol()));
    pd.setRealisedVol(new ProviderDataValue(currData.getRealisedVol(), prevData.getRealisedVol()));
    pd.setFairVol(new ProviderDataValue(currData.getFairVol(), prevData.getFairVol()));
    return pd;
  }

  public Map<String, Map<String, BigDecimal>> toPvs() {
    return valuationData.entrySet().stream()
        .collect(toMap(Map.Entry::getKey, e -> e.getValue().toPvs()));
  }
}
