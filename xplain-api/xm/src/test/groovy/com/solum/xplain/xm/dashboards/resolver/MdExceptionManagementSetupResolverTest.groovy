package com.solum.xplain.xm.dashboards.resolver

import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.DASHBOARD_DATE
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.MARKET_DATA_GROUP
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.MARKET_DATA_GROUP_ANOTHER
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.STATE_DATE

import com.solum.xplain.core.common.daterange.DateRange
import com.solum.xplain.core.common.team.EntityTeamFilter
import com.solum.xplain.core.common.value.EntityNameView
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.company.entity.Company
import com.solum.xplain.core.company.repository.CompanyLegalEntityValuationSettingsRepository
import com.solum.xplain.core.company.repository.CompanyRepository
import com.solum.xplain.core.company.value.CompanyLegalEntityValuationSettingsView
import com.solum.xplain.core.curveconfiguration.CurveConfigurationInstrumentResolver
import com.solum.xplain.core.curveconfiguration.CurveConfigurationValuationsResolver
import com.solum.xplain.core.curvegroup.instrument.CoreAssetGroup
import com.solum.xplain.core.error.Error
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.market.repository.MarketDataGroupRepository
import com.solum.xplain.core.mdvalue.MarketDataValueRepository
import com.solum.xplain.trs.company.entity.LegalEntityDataProvidersSettings
import com.solum.xplain.trs.market.TrsMarketDataGroupRepository
import com.solum.xplain.trs.value.TrsAssetClassGroup
import com.solum.xplain.xm.dashboards.entity.MdUniqueValuationSettings
import com.solum.xplain.xm.dashboards.entity.MdValuationSetting
import com.solum.xplain.xm.dashboards.validator.MdExceptionManagementSetupValidator
import com.solum.xplain.xm.excmngmt.process.CompanyEntityDataProviderSettingsResolver
import io.atlassian.fugue.Either
import java.time.LocalDate
import java.util.stream.Stream
import spock.lang.Specification

class MdExceptionManagementSetupResolverTest extends Specification {

  private static final BitemporalDate BITEMPORAL_STATE_DATE = BitemporalDate.newOf(STATE_DATE)
  private static final BitemporalDate BITEMPORAL_DASHBOARD_DATE = BitemporalDate.newOf(DASHBOARD_DATE, BITEMPORAL_STATE_DATE.getRecordDate())

  MarketDataGroupRepository marketDataGroupRepository = Mock()
  TrsMarketDataGroupRepository trsMarketDataGroupRepository = Mock()
  MarketDataValueRepository marketDataValueRepository = Mock()
  CurveConfigurationValuationsResolver curveConfigurationValuationsResolver = Mock()
  CompanyEntityDataProviderSettingsResolver trsSettingsResolver = Mock()
  MdExceptionManagementSetupValidator mdSetupValidator = Mock()
  CompanyRepository companyRepository = Mock()
  CompanyLegalEntityValuationSettingsRepository companyLegalEntityValuationSettingsRepository = Mock()

  MdExceptionManagementSetupResolver resolver = new MdExceptionManagementSetupResolver(
  marketDataGroupRepository,
  Optional.of(trsMarketDataGroupRepository),
  marketDataValueRepository,
  curveConfigurationValuationsResolver,
  Optional.of(trsSettingsResolver),
  mdSetupValidator,
  companyRepository,
  companyLegalEntityValuationSettingsRepository
  )

  def "should resolve setup"() {
    setup:
    EntityNameView mdg = Mock()
    mdg.id >> MARKET_DATA_GROUP.entityId
    mdg.name >> MARKET_DATA_GROUP.name
    1 * marketDataGroupRepository.dataGroupName(MARKET_DATA_GROUP.entityId) >> Optional.of(mdg)
    1 * marketDataValueRepository.getLatestMarketDataDate(MARKET_DATA_GROUP.entityId, BITEMPORAL_DASHBOARD_DATE) >> Either.right(STATE_DATE)

    CurveConfigurationInstrumentResolver configurationResolver = Mock()
    1 * curveConfigurationValuationsResolver.curveConfigResolversByMarketData(BITEMPORAL_STATE_DATE) >>
      Map.of(MARKET_DATA_GROUP.entityId, List.of(configurationResolver))

    1 * mdSetupValidator.validate(BITEMPORAL_STATE_DATE, DASHBOARD_DATE, _, Arrays.asList(CoreAssetGroup.values())) >> List.of()

    when:
    def result = resolver.resolve(BITEMPORAL_STATE_DATE, DASHBOARD_DATE, MARKET_DATA_GROUP.entityId)

    then:
    result.isRight()

    def setup = result.getOrNull().get()
    setup.marketDataGroup == MARKET_DATA_GROUP
    setup.previousDate == STATE_DATE
    setup.curveConfigurationResolvers.size() == 1
    setup.curveConfigurationResolvers[0] == configurationResolver
  }

  def "should resolve setup (when enabling flag)"() {
    setup:
    EntityNameView mdg = Mock()
    mdg.id >> MARKET_DATA_GROUP.entityId
    mdg.name >> MARKET_DATA_GROUP.name
    1 * marketDataGroupRepository.dataGroupName(MARKET_DATA_GROUP.entityId) >> Optional.of(mdg)
    1 * marketDataValueRepository.getLatestMarketDataDate(MARKET_DATA_GROUP.entityId, BITEMPORAL_DASHBOARD_DATE) >> Either.right(STATE_DATE)

    CurveConfigurationInstrumentResolver configurationResolver = Mock()
    1 * curveConfigurationValuationsResolver.curveConfigResolversByMarketData(BITEMPORAL_STATE_DATE) >>
      Map.of(MARKET_DATA_GROUP.entityId, List.of(configurationResolver))

    1 * mdSetupValidator.validate(BITEMPORAL_STATE_DATE, DASHBOARD_DATE, _, Arrays.asList(CoreAssetGroup.values())) >> List.of()

    def company1 = new Company(id: "company1")
    def company2 = new Company(id: "company2")
    def valuationSettings1 = new CompanyLegalEntityValuationSettingsView(entityId: "entity1", marketDataGroupId: MARKET_DATA_GROUP.entityId, configurationType: "C1")
    def valuationSettings2 = new CompanyLegalEntityValuationSettingsView(entityId: "entity2", marketDataGroupId: MARKET_DATA_GROUP.entityId, configurationType: "C2")
    def valuationSettings3 = new CompanyLegalEntityValuationSettingsView(entityId: "entity3", marketDataGroupId: MARKET_DATA_GROUP_ANOTHER.entityId, configurationType: "C1")
    def valuationSettings4 = new CompanyLegalEntityValuationSettingsView(entityId: "entity4", marketDataGroupId: MARKET_DATA_GROUP.entityId, configurationType: "C1")

    companyRepository.companyList(EntityTeamFilter.emptyFilter()) >> [company1, company2]
    companyLegalEntityValuationSettingsRepository.getCompanyLegalEntitySettingsOfCompanies([company1.id, company2.id], BITEMPORAL_STATE_DATE) >>
    Stream.of(valuationSettings1, valuationSettings2, valuationSettings3, valuationSettings4)

    when:
    def result = resolver.resolve(BITEMPORAL_STATE_DATE, DASHBOARD_DATE, MARKET_DATA_GROUP.entityId, true)

    then:
    result.isRight()

    def setup = result.getOrNull().get()
    setup.marketDataGroup == MARKET_DATA_GROUP
    setup.previousDate == STATE_DATE
    setup.curveConfigurationResolvers.size() == 1
    setup.curveConfigurationResolvers[0] == configurationResolver
    setup.applicableLegalEntitiesSettings.size() == 2
    setup.applicableLegalEntitiesSettings.containsAll([
      new MdUniqueValuationSettings(new MdValuationSetting("C1", null, null, null, null, null, null, null, null), ["entity1", "entity4"]),
      new MdUniqueValuationSettings(new MdValuationSetting("C2", null, null, null, null, null, null, null, null), ["entity2"])
    ])
  }

  def "should resolve batch setup"() {
    setup:
    EntityNameView mdg = Mock()
    mdg.id >> MARKET_DATA_GROUP.entityId
    mdg.name >> MARKET_DATA_GROUP.name
    1 * marketDataGroupRepository.dataGroupName(MARKET_DATA_GROUP.entityId) >> Optional.of(mdg)
    0 * marketDataValueRepository._

    CurveConfigurationInstrumentResolver configurationResolver = Mock()
    1 * curveConfigurationValuationsResolver.curveConfigResolversByMarketData(BITEMPORAL_STATE_DATE) >>
      Map.of(MARKET_DATA_GROUP.entityId, List.of(configurationResolver))

    1 * mdSetupValidator.validateBatch(BITEMPORAL_STATE_DATE, DateRange.newOf(DASHBOARD_DATE), _, Arrays.asList(CoreAssetGroup.values())) >> List.of()

    when:
    def result = resolver.resolveBatch(BITEMPORAL_STATE_DATE, DateRange.newOf(DASHBOARD_DATE), MARKET_DATA_GROUP.entityId)

    then:
    result.isRight()

    def setup = result.getOrNull().get()
    setup.marketDataGroup == MARKET_DATA_GROUP
    setup.previousDate == null
    setup.curveConfigurationResolvers.size() == 1
    setup.curveConfigurationResolvers[0] == configurationResolver
  }

  def "should fail resolve setup when MDG does not exist"() {
    setup:
    1 * marketDataGroupRepository.dataGroupName(MARKET_DATA_GROUP.entityId) >> Optional.empty()
    0 * mdSetupValidator._
    0 * curveConfigurationValuationsResolver._
    0 * marketDataValueRepository._

    when:
    def result = resolver.resolve(BITEMPORAL_STATE_DATE, DASHBOARD_DATE, MARKET_DATA_GROUP.entityId)

    then:
    result.isLeft()

    def errors = result.left().getOrNull() as List<ErrorItem>
    errors[0].reason == Error.OBJECT_NOT_FOUND
    errors[0].description == "Market data group not found"
  }

  def "should resolve empty setup when no MDG is provided"() {
    setup:
    0 * marketDataGroupRepository._
    0 * mdSetupValidator._
    0 * curveConfigurationValuationsResolver._
    0 * marketDataValueRepository._

    when:
    def result = resolver.resolve(BITEMPORAL_STATE_DATE, DASHBOARD_DATE, "")

    then:
    result.isRight()

    result.getOrNull().isEmpty()
  }

  def "should resolve empty batch setup when no MDG is provided"() {
    setup:
    0 * marketDataGroupRepository._
    0 * mdSetupValidator._
    0 * curveConfigurationValuationsResolver._
    0 * marketDataValueRepository._

    when:
    def result = resolver.resolveBatch(BITEMPORAL_STATE_DATE, DateRange.newOf(DASHBOARD_DATE), "")

    then:
    result.isRight()

    result.getOrNull().isEmpty()
  }

  def "should resolve trs setup"() {
    setup:
    EntityNameView mdg = Mock()
    mdg.id >> MARKET_DATA_GROUP.entityId
    mdg.name >> MARKET_DATA_GROUP.name
    1 * trsMarketDataGroupRepository.dataGroupName(MARKET_DATA_GROUP.entityId) >> Optional.of(mdg)
    1 * marketDataValueRepository.getLatestMarketDataDate(MARKET_DATA_GROUP.entityId, BITEMPORAL_DASHBOARD_DATE) >> Either.right(LocalDate.now())

    LegalEntityDataProvidersSettings entitySettings = Mock()
    1 * trsSettingsResolver.legalEntityDataProviderSettings(MARKET_DATA_GROUP.entityId, BITEMPORAL_STATE_DATE) >> List.of(entitySettings)

    1 * mdSetupValidator.validateTrs(BITEMPORAL_STATE_DATE, DASHBOARD_DATE, _, Arrays.asList(TrsAssetClassGroup.values())) >> List.of()

    when:
    def result = resolver.resolveTrs(BITEMPORAL_STATE_DATE, DASHBOARD_DATE, MARKET_DATA_GROUP.entityId)

    then:
    result.isRight()

    def setup = result.getOrNull().get()
    setup.marketDataGroup == MARKET_DATA_GROUP
    setup.previousDate == LocalDate.now()
    setup.entitySettings == [entitySettings]
  }

  def "should resolve batch trs setup"() {
    setup:
    EntityNameView mdg = Mock()
    mdg.id >> MARKET_DATA_GROUP.entityId
    mdg.name >> MARKET_DATA_GROUP.name
    1 * trsMarketDataGroupRepository.dataGroupName(MARKET_DATA_GROUP.entityId) >> Optional.of(mdg)
    0 * marketDataValueRepository._

    LegalEntityDataProvidersSettings entitySettings = Mock()
    1 * trsSettingsResolver.legalEntityDataProviderSettings(MARKET_DATA_GROUP.entityId, BITEMPORAL_STATE_DATE) >> List.of(entitySettings)

    1 * mdSetupValidator.validateBatch(BITEMPORAL_STATE_DATE, DateRange.newOf(DASHBOARD_DATE), _, Arrays.asList(TrsAssetClassGroup.values())) >> List.of()

    when:
    def result = resolver.resolveBatchTrs(BITEMPORAL_STATE_DATE, DateRange.newOf(DASHBOARD_DATE), MARKET_DATA_GROUP.entityId)

    then:
    result.isRight()

    def setup = result.getOrNull().get()
    setup.marketDataGroup == MARKET_DATA_GROUP
    setup.previousDate == null
    setup.entitySettings == [entitySettings]
  }

  def "should fail resolve trs setup when MDG does not exist"() {
    setup:
    1 * trsMarketDataGroupRepository.dataGroupName(MARKET_DATA_GROUP.entityId) >> Optional.empty()
    0 * marketDataValueRepository._
    0 * trsSettingsResolver._
    0 * mdSetupValidator._

    when:
    def result = resolver.resolveTrs(BITEMPORAL_STATE_DATE, DASHBOARD_DATE, MARKET_DATA_GROUP.entityId)

    then:
    result.isLeft()

    def errors = result.left().getOrNull() as List<ErrorItem>
    errors[0].reason == Error.OBJECT_NOT_FOUND
    errors[0].description == "TRS Market data group not found"
  }


  def "should resolve empty trs setup when no MDG is provided"() {
    setup:
    0 * trsMarketDataGroupRepository._
    0 * marketDataValueRepository._
    0 * trsSettingsResolver._
    0 * mdSetupValidator._

    when:
    def result = resolver.resolveTrs(BITEMPORAL_STATE_DATE, DASHBOARD_DATE, "")

    then:
    result.isRight()

    result.getOrNull().isEmpty()
  }

  def "should resolve empty trs batch setup when no MDG is provided"() {
    setup:
    0 * trsMarketDataGroupRepository._
    0 * marketDataValueRepository._
    0 * trsSettingsResolver._
    0 * mdSetupValidator._

    when:
    def result = resolver.resolveBatchTrs(BITEMPORAL_STATE_DATE, DateRange.newOf(DASHBOARD_DATE), "")

    then:
    result.isRight()

    result.getOrNull().isEmpty()
  }


  def "should resolve empty trs setup when no repository is available"() {
    setup:
    MdExceptionManagementSetupResolver r = new MdExceptionManagementSetupResolver(
      marketDataGroupRepository,
      Optional.empty(),
      marketDataValueRepository,
      curveConfigurationValuationsResolver,
      Optional.of(trsSettingsResolver),
      mdSetupValidator,
      companyRepository,
      companyLegalEntityValuationSettingsRepository
      )
    0 * trsMarketDataGroupRepository._
    0 * marketDataValueRepository._
    0 * trsSettingsResolver._
    0 * mdSetupValidator._

    when:
    def result = r.resolveTrs(BITEMPORAL_STATE_DATE, DASHBOARD_DATE, "")

    then:
    result.isRight()

    result.getOrNull().isEmpty()
  }

  def "should resolve empty trs setup when no settings resolver is available"() {
    setup:
    MdExceptionManagementSetupResolver r = new MdExceptionManagementSetupResolver(
      marketDataGroupRepository,
      Optional.of(trsMarketDataGroupRepository),
      marketDataValueRepository,
      curveConfigurationValuationsResolver,
      Optional.empty(),
      mdSetupValidator,
      companyRepository,
      companyLegalEntityValuationSettingsRepository
      )
    0 * trsMarketDataGroupRepository._
    0 * marketDataValueRepository._
    0 * trsSettingsResolver._
    0 * mdSetupValidator._

    when:
    def result = r.resolveTrs(BITEMPORAL_STATE_DATE, DASHBOARD_DATE, "")

    then:
    result.isRight()

    result.getOrNull().isEmpty()
  }
}
