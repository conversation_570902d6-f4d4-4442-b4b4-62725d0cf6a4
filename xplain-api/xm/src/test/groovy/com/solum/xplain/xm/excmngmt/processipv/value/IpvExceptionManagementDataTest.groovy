package com.solum.xplain.xm.excmngmt.processipv.value

import static com.solum.xplain.core.providers.DataProvider.NAV_PROVIDER_CODE

import com.solum.xplain.core.ipv.data.IpvDataType
import com.solum.xplain.core.ipv.data.value.IpvDataProviderValueView
import com.solum.xplain.xm.excmngmt.processipv.data.ProviderDataValue
import com.solum.xplain.xm.settings.IpvValueNavLevel
import spock.lang.Specification

class IpvExceptionManagementDataTest extends Specification {

  def NAV_LEVEL = IpvValueNavLevel.TRADE_LEVEL

  def "should construct clean data from raw only"() {
    setup:
    def raw = [
      new IpvDataProviderValueView(key: "1", provider: "P1", value: 1.0, delta: 2.0, vega: 3.0, notional: 1e7),
      new IpvDataProviderValueView(key: "1", provider: "P2", value: 4.0, delta: 5.0, vega: 6.0),
      new IpvDataProviderValueView(key: "2", provider: "P1", value: 7.0, delta: 8.0, vega: 9.0),
      new IpvDataProviderValueView(key: "2", provider: NAV_PROVIDER_CODE, value: 1.0, notional: 2e7),
    ]

    when:
    def result = IpvExceptionManagementData.of(raw, NAV_LEVEL)

    then:
    result.valuationData.size() == 2
    result.valuationData["1"].uniqueProviders().size() == 2
    result.valuationData["1"].uniqueProviders().containsAll(["P1", "P2"])
    result.valuationData["1"].providerData("P1").getValue() == 1.0
    result.valuationData["1"].providerData("P1").getDelta() == 2.0
    result.valuationData["1"].providerData("P2").getValue() == 4.0
    result.valuationData["1"].providerData("P1").getVega() == 3.0
    result.valuationData["1"].providerData("P2").getDelta() == 5.0
    result.valuationData["1"].providerData("P2").getVega() == 6.0
    result.valuationData["2"].uniqueProviders().size() == 1
    result.valuationData["2"].uniqueProviders() == ["P1"] as Set<String>
    result.valuationData["2"].providerData("P1").getValue() == 7.0
    result.valuationData["2"].providerData("P1").getDelta() == 8.0
    result.valuationData["2"].providerData("P1").getVega() == 9.0

    result.navData().size() == 1
    result.navData()["2"] == 1.0

    result.notionalData().size() == 1
    result.notionalData()["1"] == 1e7
  }

  def "should combine two data"() {
    setup:
    def raw = [
      new IpvDataProviderValueView(key: "1", provider: "P1", value: 1, delta: 2, vega: 3, gamma: 4, theta: 5, rho: 6, parRate: 7, spotRate: 8, impliedVol: 9, atmImpliedVol: 10, realisedVol: 11, fairVol: 12),
      new IpvDataProviderValueView(key: "2", provider: "P1", value: 7, delta: 8, vega: 9, gamma: 10, theta: 11, rho: 12, parRate: 13, spotRate: 14, impliedVol: 15, atmImpliedVol: 16, realisedVol: 17, fairVol: 18)
    ]

    def rawPrev = [
      new IpvDataProviderValueView(key: "1", provider: "P1", value: 11, delta: 12, vega: 13, gamma: 14, theta: 15, rho: 16, parRate: 17, spotRate: 18, impliedVol: 19, atmImpliedVol: 20, realisedVol: 21, fairVol: 22),
      new IpvDataProviderValueView(key: "1", provider: "P2", value: 14, delta: 15, vega: 16, gamma: 17, theta: 18, rho: 19, parRate: 20, spotRate: 21, impliedVol: 22, atmImpliedVol: 23, realisedVol: 24, fairVol: 25),
    ]

    def clean = IpvExceptionManagementData.of(raw, NAV_LEVEL)
    def cleanPrev = IpvExceptionManagementData.of(rawPrev, NAV_LEVEL)

    when:
    def result = clean.combinedData(cleanPrev)

    then:
    result.size() == 2
    result["1"].size() == 2
    result["1"][0].provider == "P1"
    result["1"][0].pv == new ProviderDataValue(1, 11)
    result["1"][0].delta == new ProviderDataValue(2, 12)
    result["1"][0].vega == new ProviderDataValue(3, 13)
    result["1"][0].gamma == new ProviderDataValue(4, 14)
    result["1"][0].theta == new ProviderDataValue(5, 15)
    result["1"][0].rho == new ProviderDataValue(6, 16)
    result["1"][0].parRate == new ProviderDataValue(7, 17)
    result["1"][0].spotRate == new ProviderDataValue(8, 18)
    result["1"][0].impliedVol == new ProviderDataValue(9, 19)
    result["1"][0].atmImpliedVol == new ProviderDataValue(10, 20)
    result["1"][0].realisedVol == new ProviderDataValue(11, 21)
    result["1"][0].fairVol == new ProviderDataValue(12, 22)
    result["1"][1].provider == "P2"
    result["1"][1].pv == new ProviderDataValue(null, 14)

    result["2"].size() == 1
    result["2"][0].provider == "P1"
    result["2"][0].pv == new ProviderDataValue(7, null)
  }

  def "should return correct pvs"() {
    setup:
    def raw = [
      new IpvDataProviderValueView(key: "1", provider: "P1", value: 1, delta: 2, vega: 3),
      new IpvDataProviderValueView(key: "1", provider: "P2", value: 4, delta: 5, vega: 6),
      new IpvDataProviderValueView(key: "2", provider: "P1", value: 7, delta: 8, vega: 9)
    ]

    def r = IpvExceptionManagementData.of(raw, NAV_LEVEL)

    when:
    def result = r.toPvs()

    then:
    result.size() == 2
    result["1"].size() == 2
    result["1"]["P1"] == 1
    result["1"]["P2"] == 4

    result["2"].size() == 1
    result["2"]["P1"] == 7
  }
}
