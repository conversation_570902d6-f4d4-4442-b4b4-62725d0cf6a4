package com.solum.xplain.xm.excmngmt.processipv

import static com.solum.xplain.core.providers.DataProvider.NAV_PROVIDER_CODE
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.DASHBOARD_DATE
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.VALUATION_DATA_GROUP
import static com.solum.xplain.xm.excmngmt.enums.IpvExceptionManagementPhase.OVERLAY_1

import com.solum.xplain.core.calculationapi.CalculationEventProducer
import com.solum.xplain.core.calculationapi.CalculationResultProvider
import com.solum.xplain.core.common.daterange.DateRange
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.ipv.data.IpvDataRepository
import com.solum.xplain.core.ipv.data.IpvDataType
import com.solum.xplain.core.ipv.data.form.IpvDataProviderValueFilter
import com.solum.xplain.core.ipv.data.value.IpvDataProviderValueView
import com.solum.xplain.xm.dashboards.events.DashboardDeletedEvent
import com.solum.xplain.xm.dashboards.repository.DashboardRepository
import com.solum.xplain.xm.dashboards.views.DashboardDateView
import com.solum.xplain.xm.excmngmt.processipv.data.ProviderDataValue
import com.solum.xplain.xm.excmngmt.processipv.data.ProviderDataWithGreeks
import com.solum.xplain.xm.excmngmt.trade.ResolvedIpvTrade
import com.solum.xplain.xm.settings.ExceptionManagementSettingsRepository
import com.solum.xplain.xm.settings.IpvValueNavLevel
import java.time.LocalDate
import java.util.stream.Stream
import org.bson.types.ObjectId
import spock.lang.Specification

class IpvExceptionManagementDataServiceTest extends Specification {
  IpvExceptionManagementCalculationRepository exceptionManagementCalculationRepository = Mock()
  CalculationResultProvider calculationResultProvider = Mock()
  IpvDataRepository ipvDataRepository = Mock()
  DashboardRepository dashboardRepository = Mock()
  CalculationEventProducer calculationEventProducer = Mock()
  ExceptionManagementSettingsRepository exceptionManagementSettingsRepository = Mock()

  IpvExceptionManagementDataService dataService = new IpvExceptionManagementDataService(
  exceptionManagementCalculationRepository,
  calculationResultProvider,
  ipvDataRepository,
  dashboardRepository,
  calculationEventProducer,
  exceptionManagementSettingsRepository
  )

  def "should return clean data for date "() {
    setup:
    def delta = new ProviderDataValue(BigDecimal.ONE, BigDecimal.ZERO)
    def vega = new ProviderDataValue(BigDecimal.TEN, BigDecimal.ONE)
    def value = new ProviderDataValue(BigDecimal.TEN, BigDecimal.ONE)
    def trade = new ResolvedIpvTrade(
      "KEY",
      DASHBOARD_DATE,
      value.getValue(),
      new ProviderDataWithGreeks(provider: "BBG", delta: delta, vega: vega, pv: value),
      null,
      null,
      null,
      null
      )

    when:
    def result = dataService.historicalExceptionManagementData(Optional.of(OVERLAY_1), "portfolioId", VALUATION_DATA_GROUP.entityId, DASHBOARD_DATE)

    then:
    1 * exceptionManagementCalculationRepository.getResolvedTradeData(Optional.of(OVERLAY_1), "portfolioId", VALUATION_DATA_GROUP.entityId, DASHBOARD_DATE) >> Stream.of(trade)

    and:
    def tradeValueView = result.valuationData().get("KEY")
    tradeValueView.providerData("BBG").value == BigDecimal.TEN
    tradeValueView.providerData("BBG").delta == BigDecimal.ONE
    tradeValueView.providerData("BBG").vega == BigDecimal.TEN
  }

  def "should return PROVIDER data"() {
    setup:
    def now = LocalDate.now()
    IpvDataProviderValueView[] rawValues = [
      new IpvDataProviderValueView(
      id: new ObjectId().toString(),
      groupId: VALUATION_DATA_GROUP.entityId,
      date: now,
      key: "COMPANY_ENTITY_PORTFOLIO_TRADE1",
      provider: "VENDOR1",
      value: BigDecimal.ONE,
      delta: BigDecimal.TEN,
      vega: BigDecimal.ZERO,
      comment: "Imported"
      ),
      new IpvDataProviderValueView(
      id: new ObjectId().toString(),
      groupId: VALUATION_DATA_GROUP.entityId,
      date: now,
      key: "COMPANY_ENTITY_PORTFOLIO_TRADE1",
      provider: "VENDOR2",
      value: BigDecimal.TEN,
      delta: BigDecimal.ONE,
      vega: BigDecimal.ZERO,
      comment: "Imported"
      ),
      new IpvDataProviderValueView(
      id: new ObjectId().toString(),
      groupId: VALUATION_DATA_GROUP.entityId,
      date: now,
      key: "COMPANY_ENTITY_PORTFOLIO_TRADE1",
      provider: NAV_PROVIDER_CODE,
      value: BigDecimal.ZERO,
      comment: "Imported"
      )
    ]

    when:
    1 * exceptionManagementSettingsRepository.getNavLevelEnum(_ as BitemporalDate) >> IpvValueNavLevel.TRADE_LEVEL
    def result = dataService.providerData(VALUATION_DATA_GROUP.entityId, now)

    then:
    with (result.valuationData()) {
      size() == 1
      with (it.get("COMPANY_ENTITY_PORTFOLIO_TRADE1")) {
        it != null
        it.uniqueProviders() == ["VENDOR1", "VENDOR2"] as Set
        with (it.providerData("VENDOR1")) {
          value == BigDecimal.ONE
          delta == BigDecimal.TEN
          vega == BigDecimal.ZERO
        }
        with (it.providerData("VENDOR2")) {
          value == BigDecimal.TEN
        }
        with (it.providerData(NAV_PROVIDER_CODE)) {
          value == null
        }
      }
    }
    with (result.navData()) {
      size() == 1
      it.get("COMPANY_ENTITY_PORTFOLIO_TRADE1") == BigDecimal.ZERO
    }

    and:
    1 * ipvDataRepository.getValueViewsStream(VALUATION_DATA_GROUP.entityId, IpvDataProviderValueFilter.newOfValuationDate(now), IpvDataType.ALL) >> Stream.of(rawValues)
  }

  def "should return PROVIDER data for ENTITY_LEVEL combining NAV and EXCLUDE_NAV data"() {
    setup:
    def now = LocalDate.now()

    // NAV data
    IpvDataProviderValueView[] navValues = [
      new IpvDataProviderValueView(
      id: new ObjectId().toString(),
      groupId: VALUATION_DATA_GROUP.entityId,
      date: now,
      key: "COMPANY_ENTITY1",
      provider: NAV_PROVIDER_CODE,
      value: BigDecimal.valueOf(100),
      comment: "NAV Imported"
      ),
      new IpvDataProviderValueView(
      id: new ObjectId().toString(),
      groupId: VALUATION_DATA_GROUP.entityId,
      date: now,
      key: "COMPANY_ENTITY2",
      provider: NAV_PROVIDER_CODE,
      value: BigDecimal.valueOf(200),
      comment: "NAV Imported"
      )
    ]

    // EXCLUDE_NAV data
    IpvDataProviderValueView[] excludeNavValues = [
      new IpvDataProviderValueView(
      id: new ObjectId().toString(),
      groupId: VALUATION_DATA_GROUP.entityId,
      date: now,
      key: "COMPANY_ENTITY_PORTFOLIO_TRADE3",
      provider: "EXCLUDE_VENDOR1",
      value: BigDecimal.valueOf(50),
      delta: BigDecimal.valueOf(2),
      vega: BigDecimal.valueOf(1),
      comment: "Exclude NAV Imported"
      ),
      new IpvDataProviderValueView(
      id: new ObjectId().toString(),
      groupId: VALUATION_DATA_GROUP.entityId,
      date: now,
      key: "COMPANY_ENTITY_PORTFOLIO_TRADE4",
      provider: "EXCLUDE_VENDOR2",
      value: BigDecimal.valueOf(75),
      delta: BigDecimal.valueOf(3),
      vega: BigDecimal.valueOf(1.5),
      comment: "Exclude NAV Imported"
      )
    ]

    when:
    1 * exceptionManagementSettingsRepository.getNavLevelEnum(_ as BitemporalDate) >> IpvValueNavLevel.ENTITY_LEVEL
    1 * ipvDataRepository.getValueViewsStream(
      VALUATION_DATA_GROUP.entityId,
      new IpvDataProviderValueFilter(now, true, false),
      IpvDataType.NAV) >> Stream.of(navValues)
    1 * ipvDataRepository.getValueViewsStream(
      VALUATION_DATA_GROUP.entityId,
      new IpvDataProviderValueFilter(now, true, false),
      IpvDataType.EXCLUDE_NAV) >> Stream.of(excludeNavValues)

    def result = dataService.providerData(VALUATION_DATA_GROUP.entityId, now)

    then:
    with (result.valuationData()) {
      size() == 2

      with (it.get("COMPANY_ENTITY_PORTFOLIO_TRADE3")) {
        it != null
        it.uniqueProviders() == ["EXCLUDE_VENDOR1"] as Set
        with (it.providerData("EXCLUDE_VENDOR1")) {
          value == BigDecimal.valueOf(50)
          delta == BigDecimal.valueOf(2)
          vega == BigDecimal.valueOf(1)
        }
      }

      with (it.get("COMPANY_ENTITY_PORTFOLIO_TRADE4")) {
        it != null
        it.uniqueProviders() == ["EXCLUDE_VENDOR2"] as Set
        with (it.providerData("EXCLUDE_VENDOR2")) {
          value == BigDecimal.valueOf(75)
          delta == BigDecimal.valueOf(3)
          vega == BigDecimal.valueOf(1.5)
        }
      }
    }

    with (result.navData()) {
      size() == 2
      it.get("COMPANY_ENTITY1") == BigDecimal.valueOf(100)
      it.get("COMPANY_ENTITY2") == BigDecimal.valueOf(200)
    }
  }

  def "should return previous dashboard"() {
    when:
    def result = dataService.previousDashboard("portfolioId", LocalDate.now())

    then:
    with (result) {
      isPresent()
      get().id() == "dashboardId"
      get().dateRange().singleDate() == LocalDate.now().minusDays(1)
    }

    and:
    1 * dashboardRepository.previousPortfolioDashboardId("portfolioId", LocalDate.now()) >>
      Optional.of(new DashboardDateView("dashboardId", DateRange.newOf(LocalDate.now().minusDays(1), LocalDate.now().minusDays(1))))
  }

  def "should return clean data for previous date"() {
    setup:
    def delta = new ProviderDataValue(BigDecimal.ONE, BigDecimal.ZERO)
    def vega = new ProviderDataValue(BigDecimal.TEN, BigDecimal.ONE)
    def value = new ProviderDataValue(BigDecimal.TEN, BigDecimal.ONE)
    def trade = new ResolvedIpvTrade(
      "KEY",
      DASHBOARD_DATE,
      value.getValue(),
      new ProviderDataWithGreeks(provider: "BBG", delta: delta, vega: vega, pv: value),
      null,
      null,
      null,
      null,
      )

    when:
    def result = dataService.previousDayExceptionManagementData(
      "portfolioId", VALUATION_DATA_GROUP.entityId, LocalDate.now())

    then:
    def tradeValueView = result.valuationData().get("KEY")
    tradeValueView.providerData("BBG").value == BigDecimal.TEN
    tradeValueView.providerData("BBG").delta == BigDecimal.ONE
    tradeValueView.providerData("BBG").vega == BigDecimal.TEN

    and:
    1 * dashboardRepository.previousPortfolioDashboardId("portfolioId", LocalDate.now()) >>
      Optional.of(new DashboardDateView("dashboardId", DateRange.newOf(DASHBOARD_DATE, DASHBOARD_DATE)))
    1 * exceptionManagementCalculationRepository.getResolvedTradeData(Optional.empty(), "portfolioId", VALUATION_DATA_GROUP.entityId, DASHBOARD_DATE) >> Stream.of(trade)
  }

  def "should clean up calculations where dashboard is deleted"() {
    given:
    def calculationId = ObjectId.get()

    when:
    dataService.cleanupCalculationData(DashboardDeletedEvent.newOf("id1"))

    then:
    1 * calculationResultProvider.latestDashboardCalculationsByPortfolioId("id1") >> ["p1":calculationId]
    1 * calculationEventProducer.publishDeleteEvent(calculationId.toHexString())
  }
}
