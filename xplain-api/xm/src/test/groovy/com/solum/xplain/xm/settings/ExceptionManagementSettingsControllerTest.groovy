package com.solum.xplain.xm.settings

import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post

import com.fasterxml.jackson.databind.ObjectMapper
import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.common.value.DateList
import com.solum.xplain.core.common.value.NewVersionFormV2
import com.solum.xplain.core.test.TestSecurityConfig
import com.solum.xplain.xm.helpers.MockMvcConfiguration
import com.solum.xplain.xm.settings.value.ExceptionManagementSettingsForm
import com.solum.xplain.xm.settings.value.ExceptionManagementSettingsView
import io.atlassian.fugue.Either
import java.time.LocalDate
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.context.annotation.Import
import org.springframework.http.MediaType
import org.springframework.security.test.context.support.WithMockUser
import org.springframework.test.web.servlet.MockMvc
import spock.lang.Specification

@MockMvcConfiguration
@WebMvcTest(controllers = [ExceptionManagementSettingsController])
@Import([TestSecurityConfig])
class ExceptionManagementSettingsControllerTest extends Specification {
  static URI = "/settings/exception-management"


  @SpringBean
  ExceptionManagementSettingsControllerService service = Mock()


  @Autowired
  ObjectMapper mapper

  @Autowired
  MockMvc mockMvc

  @WithMockUser(authorities = ['V_XM_S'])
  def "should get exception management settings"() {
    setup:
    1 * service.getSettings({ it.getActualDate() == LocalDate.parse("2020-01-01") }) >> new ExceptionManagementSettingsView(IpvValueCurrencyType.TRADE_CCY, IpvValueNavLevel.TRADE_LEVEL)

    when:
    def results = mockMvc.perform(get(URI)
      .param("stateDate", "2020-01-01")
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results != null
    with(results.getResponse()) {
      getStatus() == 200
      getContentAsString().indexOf("currencyType") >= 0
    }
  }

  @WithMockUser(authorities = ['V_XM_S'])
  def "should get exception management settings versions"() {
    setup:
    1 * service.getSettingsVersions() >> []

    when:
    def results = mockMvc.perform(get(URI + "/versions")
      .param("stateDate", "2020-01-01")
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results != null
    with(results.getResponse()) {
      getStatus() == 200
      getContentAsString().indexOf("[]") >= 0
    }
  }

  @WithMockUser(authorities = ['V_XM_S'])
  def "should get exception management settings future versions"() {
    setup:
    1 * service.futureVersions(LocalDate.parse("2020-01-01")) >> DateList.uniqueSorted([])

    when:
    def results = mockMvc.perform(get(URI + "/future-versions/search")
      .param("stateDate", "2020-01-01")
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results != null
    with(results.getResponse()) {
      getStatus() == 200
      getContentAsString().indexOf("[]") >= 0
    }
  }

  @WithMockUser(authorities = ['M_XM_S'])
  def "should update exception management settings version"() {
    setup:
    def version = LocalDate.now()
    service.updateSettings(version, _ as ExceptionManagementSettingsForm) >> Either.right(EntityId.entityId("id"))

    when:
    def results = mockMvc.perform(post(URI + "/{version}", version)
      .contentType(MediaType.APPLICATION_JSON)
      .content(mapper.writeValueAsString(form))
      .with(csrf()))
      .andReturn()

    then:
    results != null
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(response) >= 0
    }

    where:
    form                                      | code | response
    form()                                    | 200  | "id"
    form({ it -> it.remove("currencyType") }) | 412  | "NotNull.exceptionManagementSettingsForm.currencyType"
    form({ it -> it.remove("navLevel") })     | 412  | "NotNull.exceptionManagementSettingsForm.navLevel"
    form({ it -> it.remove("versionForm") })  | 412  | "NotNull.exceptionManagementSettingsForm.versionForm"
  }

  def "should deny access without VIEW_XM_SETTINGS permission"() {
    when:
    def results = mockMvc.perform(get(URI)
      .param("stateDate", "2020-01-01")
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results.getResponse().getStatus() == 403
  }

  def "should deny access without MODIFY_XM_SETTINGS permission for updates"() {
    setup:
    def version = LocalDate.now()

    when:
    def results = mockMvc.perform(post(URI + "/{version}", version)
      .contentType(MediaType.APPLICATION_JSON)
      .content(mapper.writeValueAsString(form()))
      .with(csrf()))
      .andReturn()

    then:
    results.getResponse().getStatus() == 403
  }

  @WithMockUser(authorities = ['V_XM_S'])
  def "should allow access with VIEW_XM_SETTINGS permission"() {
    setup:
    1 * service.getSettings({ it.getActualDate() == LocalDate.parse("2020-01-01") }) >>
    new ExceptionManagementSettingsView(IpvValueCurrencyType.TRADE_CCY, IpvValueNavLevel.TRADE_LEVEL)

    when:
    def results = mockMvc.perform(get(URI)
      .param("stateDate", "2020-01-01")
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results.getResponse().getStatus() == 200
  }

  @WithMockUser(authorities = ['M_XM_S'])
  def "should allow updates with MODIFY_XM_SETTINGS permission"() {
    setup:
    def version = LocalDate.now()
    service.updateSettings(version, _ as ExceptionManagementSettingsForm) >> Either.right(EntityId.entityId("id"))

    when:
    def results = mockMvc.perform(post(URI + "/{version}", version)
      .contentType(MediaType.APPLICATION_JSON)
      .content(mapper.writeValueAsString(form()))
      .with(csrf()))
      .andReturn()

    then:
    results.getResponse().getStatus() == 200
  }


  def form(Closure c = { f -> f }) {
    [
      currencyType: IpvValueCurrencyType.TRADE_CCY,
      navLevel : IpvValueNavLevel.TRADE_LEVEL,
      versionForm : NewVersionFormV2.newDefault()
    ].tap(c)
  }
}
