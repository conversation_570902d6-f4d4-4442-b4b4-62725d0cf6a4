package com.solum.xplain.calibration.rates

import static com.opengamma.strata.product.swap.type.FixedOvernightSwapConventions.USD_FIXED_TERM_SOFR_OIS
import static com.solum.xplain.core.classifiers.CurveNodeTypes.FIXED_OVERNIGHT_SWAP_NODE
import static com.solum.xplain.core.curvegroup.curve.entity.CurveNodeBuilder.curveNode

import com.opengamma.strata.basics.ReferenceData
import com.opengamma.strata.basics.currency.Currency
import com.opengamma.strata.basics.date.DayCounts
import com.opengamma.strata.basics.index.IborIndices
import com.opengamma.strata.basics.index.OvernightIndices
import com.opengamma.strata.data.ImmutableMarketData
import com.opengamma.strata.data.MarketData
import com.opengamma.strata.data.MarketDataFxRateProvider
import com.opengamma.strata.market.ValueType
import com.opengamma.strata.market.curve.Curve
import com.opengamma.strata.market.curve.CurveDefinition
import com.opengamma.strata.market.curve.CurveGroupName
import com.opengamma.strata.market.curve.CurveName
import com.opengamma.strata.market.curve.CurveNode
import com.opengamma.strata.market.curve.DefaultCurveMetadata
import com.opengamma.strata.market.curve.InterpolatedNodalCurve
import com.opengamma.strata.market.curve.InterpolatedNodalCurveDefinition
import com.opengamma.strata.market.curve.RatesCurveGroupDefinition
import com.opengamma.strata.market.curve.RatesCurveGroupEntry
import com.opengamma.strata.market.curve.interpolator.CurveExtrapolators
import com.opengamma.strata.market.curve.interpolator.CurveInterpolators
import com.opengamma.strata.market.curve.node.FixedOvernightSwapCurveNode
import com.opengamma.strata.pricer.rate.ImmutableRatesProvider
import com.solum.xplain.extensions.index.OffshoreIndices
import java.time.LocalDate
import spock.lang.Specification

class CalibrationUtilsTest extends Specification {

  def "should always keep offshore curves"() {
    given:
    def usdSofrDef = Mock(CurveDefinition, {
      getName() >> CurveName.of("USD SOFR")
    })
    def thb6mOffshoreDef = Mock(CurveDefinition, {
      getName() >> CurveName.of("THB 6M OFFSHORE")
    })
    def thbUsdDef = Mock(CurveDefinition, {
      getName() >> CurveName.of("THB/USD")
    })
    def usdSofr = Mock(Curve, {
      getName() >> CurveName.of("USD SOFR")
    })
    def thb6mOffshore = Mock(Curve, {
      getName() >> CurveName.of("THB 6M OFFSHORE")
    })
    def thbUsd = Mock(Curve, {
      getName() >> CurveName.of("THB/USD")
    })
    def usdCurveGroup = RatesCurveGroupDefinition.of(CurveGroupName.of("USD"),
    [RatesCurveGroupEntry.builder()
      .curveName(CurveName.of("USD SOFR"))
      .indices(OvernightIndices.USD_SOFR)
      .build(),
      RatesCurveGroupEntry.builder().curveName(CurveName.of("THB/USD"))
      .discountCurrencies(Currency.USD)
      .build(),
      RatesCurveGroupEntry.builder().curveName(CurveName.of("THB 6M OFFSHORE"))
      .indices(OffshoreIndices.THB_THBFIX_OFFSHORE_6M)
      .build()], [usdSofrDef, thb6mOffshoreDef, thbUsdDef]
    )
    def valuationDate = LocalDate.of(2023, 10, 1)
    def usdRatesResult = new RatesCalibrationResult(
    ImmutableRatesProvider.builder(valuationDate)
    .fxRateProvider(Mock(MarketDataFxRateProvider))
    .discountCurve(Currency.USD, usdSofr)
    .discountCurve(Currency.THB, thbUsd)
    .indexCurve(OffshoreIndices.THB_THBFIX_OFFSHORE_6M, thb6mOffshore)
    .indexCurve(OvernightIndices.USD_SOFR, usdSofr)
    .build(),
    []
    )
    def usdIsdaRatesSupplier = () -> Either.right(usdRatesResult)
    List<CalibrationSetResult> results = [
      new CalibrationSetResult(usdCurveGroup, usdRatesResult, usdIsdaRatesSupplier)]
    MarketData marketData = Mock(ImmutableMarketData, {
      getValuationDate() >> valuationDate
    })
    ReferenceData referenceData = Mock(ReferenceData)


    when:
    def result = CalibrationUtils.calibrationCombinedResultRates(
    results
    ,null,
    marketData,
    referenceData
    )

    then:
    result.getRatesProvider().getCurves().size() == 3
    result.getCurves()*.name*.name.sort(false) == ["THB 6M OFFSHORE", "THB/USD", "USD SOFR"]
  }

  def "should drop index curves when they don't match the discount curves for multi-currency"() {
    given:
    def cadCorraDef = Mock(CurveDefinition, {
      getName() >> CurveName.of("CAD CORRA")
    })
    def usdCadDef = Mock(CurveDefinition, {
      getName() >> CurveName.of("USD/CAD")
    })
    def usdSofrDef = Mock(CurveDefinition, {
      getName() >> CurveName.of("USD SOFR")
    })
    def cadCorra = Mock(Curve, {
      getName() >> CurveName.of("CAD CORRA")
    })
    def usdCad = Mock(Curve, {
      getName() >> CurveName.of("USD/CAD")
    })
    def usdSofr = Mock(Curve, {
      getName() >> CurveName.of("USD SOFR")
    })

    def cadCurveGroup = RatesCurveGroupDefinition.of(CurveGroupName.of("CAD"),
    [RatesCurveGroupEntry.builder()
      .curveName(CurveName.of("CAD CORRA"))
      .discountCurrencies(Currency.CAD)
      .indices(OvernightIndices.CAD_CORRA)
      .build()], [cadCorraDef
    ])

    def usdCurveGroup = RatesCurveGroupDefinition.of(CurveGroupName.of("USD"),
    [RatesCurveGroupEntry.builder()
      .curveName(CurveName.of("CAD CORRA"))
      .discountCurrencies(Currency.CAD)
      .indices(OvernightIndices.CAD_CORRA)
      .build(),
      RatesCurveGroupEntry.builder()
      .curveName(CurveName.of("USD/CAD"))
      .discountCurrencies(Currency.USD)
      .build(),
      RatesCurveGroupEntry.builder()
      .curveName(CurveName.of("USD SOFR"))
      .indices(OvernightIndices.USD_SOFR)
      .build()], [cadCorraDef, usdCadDef, usdSofrDef
    ])
    def valuationDate = LocalDate.of(2023, 10, 1)
    def cadRatesResult = new RatesCalibrationResult(
    ImmutableRatesProvider.builder(valuationDate)
    .fxRateProvider(Mock(MarketDataFxRateProvider))
    .discountCurve(Currency.CAD, cadCorra)
    .indexCurve(OvernightIndices.CAD_CORRA, cadCorra)
    .build(),
    []
    )
    def usdRatesResult = new RatesCalibrationResult(
    ImmutableRatesProvider.builder(valuationDate)
    .fxRateProvider(Mock(MarketDataFxRateProvider))
    .discountCurve(Currency.USD, usdCad)
    .discountCurve(Currency.CAD, cadCorra)
    .indexCurve(OvernightIndices.CAD_CORRA, cadCorra)
    .indexCurve(OvernightIndices.USD_SOFR, usdSofr)
    .build(),
    []
    )
    def cadIsdaRatesSupplier = () -> Either.right(cadRatesResult)
    def usdIsdaRatesSupplier = () -> Either.right(usdRatesResult)
    List<CalibrationSetResult> results = [
      new CalibrationSetResult(cadCurveGroup, cadRatesResult, cadIsdaRatesSupplier),
      new CalibrationSetResult(usdCurveGroup, usdRatesResult, usdIsdaRatesSupplier)]
    MarketData marketData = Mock(ImmutableMarketData, {
      getValuationDate() >> valuationDate
    })
    ReferenceData referenceData = Mock(ReferenceData)

    when:
    def result = CalibrationUtils.calibrationCombinedResultRates(
    results
    ,null,
    marketData,
    referenceData
    )

    then:
    result.getRatesProvider().getCurves().size() == 2
    result.getCurves()*.name*.name.sort(false) == ["CAD CORRA", "USD/CAD"]
  }

  def "should keep curves when the discounting curves are single currency"() {
    given:
    def usdSofrDef = Mock(CurveDefinition, {
      getName() >> CurveName.of("USD SOFR")
    })
    def usd3mDef = Mock(CurveDefinition, {
      getName() >> CurveName.of("USD 3M")
    })
    def usdSofr = Mock(Curve, {
      getName() >> CurveName.of("USD SOFR")
    })
    def usd3m = Mock(Curve, {
      getName() >> CurveName.of("USD 3M")
    })

    def usdCurveGroup = RatesCurveGroupDefinition.of(CurveGroupName.of("USD"),
    [RatesCurveGroupEntry.builder()
      .curveName(CurveName.of("USD SOFR"))
      .indices(OvernightIndices.USD_SOFR)
      .build(), RatesCurveGroupEntry.builder()
      .curveName(CurveName.of("USD 3M")).indices(IborIndices.USD_LIBOR_3M).build()], [usdSofrDef, usd3mDef]
    )
    def valuationDate = LocalDate.of(2023, 10, 1)
    def usdRatesResult = new RatesCalibrationResult(
    ImmutableRatesProvider.builder(valuationDate)
    .fxRateProvider(Mock(MarketDataFxRateProvider))
    .discountCurve(Currency.USD, usdSofr)
    .indexCurve(OvernightIndices.USD_SOFR, usdSofr)
    .indexCurve(IborIndices.USD_LIBOR_3M, usd3m)
    .build(),
    []
    )
    def usdIsdaRatesSupplier = () -> Either.right(usdRatesResult)
    List<CalibrationSetResult> results = [
      new CalibrationSetResult(usdCurveGroup, usdRatesResult, usdIsdaRatesSupplier)]
    MarketData marketData = Mock(ImmutableMarketData, {
      getValuationDate() >> valuationDate
    })
    ReferenceData referenceData = Mock(ReferenceData)

    when:
    def result = CalibrationUtils.calibrationCombinedResultRates(
    results
    ,null,
    marketData,
    referenceData
    )

    then:
    result.getRatesProvider().getCurves().size() == 2
    result.getCurves()*.name*.name.sort(false) == ["USD 3M", "USD SOFR"]
  }
}
