package com.solum.xplain.support.migration.changeunits.v_2_02_0

import static com.solum.xplain.support.migration.changeunits.v_2_02_0.CU07ReplacePortfolioItemVdkIndex.COLLECTION

import com.solum.xplain.core.helper.IntegrationSpecification
import jakarta.annotation.Resource
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.index.Index
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class CU07ReplacePortfolioItemVdkIndexTest extends IntegrationSpecification {
  @Resource
  MongoTemplate mongoTemplate

  def cleanup() {
    mongoTemplate.dropCollection(COLLECTION)
  }

  def "should create indices on fresh database"() {
    setup:
    def changeUnit = new CU07ReplacePortfolioItemVdkIndex(mongoTemplate)

    when:
    changeUnit.beforeExecution()

    then:
    def peInfo = mongoTemplate.indexOps(COLLECTION).getIndexInfo()
    peInfo.find { it.getName() == CU07ReplacePortfolioItemVdkIndex.VDK_INDEX_NAME } != null

    when: "attempting to recreate when it already exists"
    changeUnit.beforeExecution()

    then: "should not throw an exception"
    noExceptionThrown()
  }

  def "should remove old index called valuationDataKey_1"() {
    setup:
    def changeUnit = new CU07ReplacePortfolioItemVdkIndex(mongoTemplate)
    // Create the old index first
    mongoTemplate.indexOps(COLLECTION).ensureIndex(
      new Index().on("valuationDataKey", Sort.Direction.ASC).named("valuationDataKey_1")
      )

    when:
    changeUnit.beforeExecution()

    then:
    def peInfo = mongoTemplate.indexOps(COLLECTION).getIndexInfo()
    peInfo.find { it.getName() == "valuationDataKey_1" } == null
  }

  def "should remove any old index on valuationDataKey even if it has a different name"() {
    setup:
    def changeUnit = new CU07ReplacePortfolioItemVdkIndex(mongoTemplate)
    // Create an old index with a different name
    mongoTemplate.indexOps(COLLECTION).ensureIndex(
      new Index().on("valuationDataKey", Sort.Direction.ASC).named("old_valuationDataKey_index")
      )

    when:
    changeUnit.beforeExecution()

    then:
    def peInfo = mongoTemplate.indexOps(COLLECTION).getIndexInfo()
    peInfo.find { it.getName() == "old_valuationDataKey_index" } == null
  }

  def "should not remove any composite index involving valuationDataKey"() {
    setup:
    def changeUnit = new CU07ReplacePortfolioItemVdkIndex(mongoTemplate)
    // Create a composite index that includes valuationDataKey
    mongoTemplate.indexOps(COLLECTION).ensureIndex(
      new Index().on("valuationDataKey", Sort.Direction.ASC).on("anotherField", Sort.Direction.DESC).named("composite_index")
      )

    when:
    changeUnit.beforeExecution()

    then:
    def peInfo = mongoTemplate.indexOps(COLLECTION).getIndexInfo()
    peInfo.find { it.getName() == "composite_index" } != null
  }
}
