package com.solum.xplain.support.migration.changeunits.v0

import com.solum.xplain.calculation.CalculationPortfolioItem
import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.portfolio.PortfolioItem
import com.solum.xplain.core.portfolio.PortfolioItemEntity
import com.solum.xplain.core.providers.DataProvider
import com.solum.xplain.core.providers.enums.DataProviderType
import com.solum.xplain.core.settings.entity.ConvexityAdjustmentsSettings
import com.solum.xplain.core.settings.entity.CurveStrippingProductSettings
import com.solum.xplain.core.settings.entity.GlobalValuationSettings
import com.solum.xplain.core.settings.entity.InflationSeasonalitySettings
import com.solum.xplain.core.settings.entity.MdTaskDefaultTeams
import com.solum.xplain.xm.excmngmt.rules.BreakTest
import com.solum.xplain.xm.excmngmt.rulesipv.IpvBreakTest
import jakarta.annotation.Resource
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.query.Query
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class ChangeUnit001Test extends IntegrationSpecification {
  @Resource
  MongoTemplate mongoTemplate

  def cleanup() {
    mongoTemplate.dropCollection(DataProvider)
    mongoTemplate.dropCollection(MdTaskDefaultTeams)
    mongoTemplate.dropCollection(BreakTest)
    mongoTemplate.dropCollection(IpvBreakTest)
    mongoTemplate.dropCollection(CalculationPortfolioItem)
  }

  def "should create indices"() {
    setup:
    def changeUnit = new ChangeUnit001(mongoTemplate)

    when:
    changeUnit.beforeExecution()

    then:
    mongoTemplate.indexOps(PortfolioItem).getIndexInfo().size() == 6
    mongoTemplate.indexOps(PortfolioItemEntity).getIndexInfo().size() == 4
  }

  def "should create initial data"() {
    setup:
    def changeUnit = new ChangeUnit001(mongoTemplate)

    when:
    changeUnit.execute()

    then:
    def loadedProvider = mongoTemplate.findAll(DataProvider.class)
    loadedProvider.size() == 1
    loadedProvider[0].getExternalId() == DataProvider.XPLAIN_PROVIDER_CODE
    loadedProvider[0].getName() == DataProvider.XPLAIN_PROVIDER_CODE
    loadedProvider[0].getTypes() == Set.of(DataProviderType.VALUATION)

    mongoTemplate.exists(new Query(), MdTaskDefaultTeams.class)

    def loadedBreakTests = mongoTemplate.findAll(BreakTest.class)
    loadedBreakTests.size() == 2

    def loadedIpvBreakTests = mongoTemplate.findAll(IpvBreakTest.class)
    loadedIpvBreakTests.size() == 1
  }

  def "should skip changelog if provider exists "() {
    setup:
    mongoTemplate.insert(new DataProvider(externalId: DataProvider.XPLAIN_PROVIDER_CODE))
    def changeUnit = new ChangeUnit001(mongoTemplate)

    when:
    changeUnit.execute()

    then:
    def loadedProvider = mongoTemplate.findAll(DataProvider.class)
    loadedProvider.size() == 1
    !mongoTemplate.exists(new Query(), CurveStrippingProductSettings.class)
    !mongoTemplate.exists(new Query(), InflationSeasonalitySettings.class)
    !mongoTemplate.exists(new Query(), ConvexityAdjustmentsSettings.class)
    !mongoTemplate.exists(new Query(), GlobalValuationSettings.class)
    !mongoTemplate.exists(new Query(), MdTaskDefaultTeams.class)

    def loadedBreakTests = mongoTemplate.findAll(BreakTest.class)
    loadedBreakTests.size() == 0

    def loadedIpvBreakTests = mongoTemplate.findAll(IpvBreakTest.class)
    loadedIpvBreakTests.size() == 0
  }

  def "should run empty rollback"() {
    setup:
    def changeUnit = new ChangeUnit001(mongoTemplate)

    expect:
    changeUnit.rollback()
  }
}
