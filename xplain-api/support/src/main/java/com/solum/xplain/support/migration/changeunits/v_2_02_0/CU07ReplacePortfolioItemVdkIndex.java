package com.solum.xplain.support.migration.changeunits.v_2_02_0;

import static com.solum.xplain.support.migration.changeunits.ChangeUnitSupport.indexName;

import com.solum.xplain.support.migration.changeunits.ChangeUnitSupport;
import io.mongock.api.annotations.BeforeExecution;
import io.mongock.api.annotations.ChangeUnit;
import io.mongock.api.annotations.Execution;
import io.mongock.api.annotations.RollbackBeforeExecution;
import io.mongock.api.annotations.RollbackExecution;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.index.Index;
import org.springframework.data.mongodb.core.index.IndexInfo;
import org.springframework.data.mongodb.core.index.PartialIndexFilter;
import org.springframework.data.mongodb.core.query.Criteria;

@RequiredArgsConstructor
@ChangeUnit(order = "v02.02.00_07", id = "v2.02.0_07", author = "xplain")
@Slf4j
public class CU07ReplacePortfolioItemVdkIndex {
  static final String COLLECTION = "portfolioItem";
  static final Map<String, Sort.Direction> INDEX_DEFINITION =
      Map.of(
          "valuationDataKey", Sort.Direction.ASC,
          "validFrom", Sort.Direction.DESC,
          "validities.recordFrom", Sort.Direction.DESC,
          "validities.recordTo", Sort.Direction.DESC,
          "validities.validTo", Sort.Direction.ASC,
          "state", Sort.Direction.ASC,
          "portfolioArchivedAt", Sort.Direction.ASC);
  static final String[] FIELDS = INDEX_DEFINITION.keySet().toArray(new String[0]);
  static final String VDK_INDEX_NAME =
      indexName("vdk", "validFrom", "recordFrom", "recordTo", "validTo", "state");
  static final String OLD_VDK_INDEX_NAME = "valuationDataKey_1";
  private final MongoTemplate mongoTemplate;

  @BeforeExecution
  public void beforeExecution() {
    dropOldIndexes();

    var hasIndex = ChangeUnitSupport.indexExists(mongoTemplate, COLLECTION, VDK_INDEX_NAME, FIELDS);

    if (hasIndex) {
      log.warn(
          "Index {} (or equivalent) already exists on {}} collection, skipping creation.",
          VDK_INDEX_NAME,
          COLLECTION);
      return;
    }

    Index index = new Index().named(VDK_INDEX_NAME);
    for (Map.Entry<String, Sort.Direction> field : INDEX_DEFINITION.entrySet()) {
      index = index.on(field.getKey(), field.getValue());
    }

    mongoTemplate
        .indexOps(COLLECTION)
        .ensureIndex(
            index.partial(PartialIndexFilter.of(Criteria.where("portfolioArchivedAt").isNull())));
  }

  private void dropOldIndexes() {
    List<String> indexesToDrop =
        mongoTemplate.indexOps(COLLECTION).getIndexInfo().stream()
            .filter(
                indexInfo ->
                    indexInfo.getName().equals(OLD_VDK_INDEX_NAME)
                        || (indexInfo.isIndexForFields(List.of("valuationDataKey"))
                            && indexInfo.getIndexFields().size() == 1))
            .map(IndexInfo::getName)
            .toList();

    indexesToDrop.forEach(
        indexInfo -> {
          log.info("Dropping existing index {} on portfolioItem", indexInfo);
          mongoTemplate.indexOps(COLLECTION).dropIndex(indexInfo);
        });
  }

  @Execution
  public void execute() {
    // Execution not needed
  }

  @RollbackExecution
  public void rollback() {
    // Rollback not needed
  }

  @RollbackBeforeExecution
  public void rollbackBefore() {
    // Rollback not needed
  }
}
