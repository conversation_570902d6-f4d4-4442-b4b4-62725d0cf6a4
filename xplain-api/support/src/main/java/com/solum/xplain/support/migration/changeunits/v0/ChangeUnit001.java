package com.solum.xplain.support.migration.changeunits.v0;

import static org.springframework.data.mongodb.core.query.Criteria.where;
import static org.springframework.data.mongodb.core.query.Query.query;

import com.solum.xplain.calculation.CalculationPortfolioItem;
import com.solum.xplain.calculation.CalculationResult;
import com.solum.xplain.calculation.comparison.entity.ComparisonResult;
import com.solum.xplain.calculation.comparison.entity.ComparisonResultItem;
import com.solum.xplain.calculation.simulation.daterange.entity.DateRangeSimulationErrorItem;
import com.solum.xplain.core.audit.entity.AuditEntry;
import com.solum.xplain.core.audit.entity.AuditEntryItem;
import com.solum.xplain.core.company.entity.CompanyLegalEntity;
import com.solum.xplain.core.fixings.Fixing;
import com.solum.xplain.core.ipv.data.entity.IpvDataValue;
import com.solum.xplain.core.market.MarketDataKey;
import com.solum.xplain.core.mdvalue.entity.MarketDataValue;
import com.solum.xplain.core.portfolio.PortfolioItem;
import com.solum.xplain.core.portfolio.PortfolioItemEntity;
import com.solum.xplain.core.providers.DataProvider;
import com.solum.xplain.core.providers.enums.DataProviderType;
import com.solum.xplain.core.settings.entity.MdTaskDefaultTeams;
import com.solum.xplain.core.teams.Team;
import com.solum.xplain.secmaster.entity.SecMasterTradeReadEntity;
import com.solum.xplain.trs.portfolio.NonMtmPortfolioItem;
import com.solum.xplain.trs.valuation.entity.NonMtmValuationPortfolioItem;
import com.solum.xplain.xm.excmngmt.process.data.InstrumentResultOverlay;
import com.solum.xplain.xm.excmngmt.process.data.InstrumentResultPreliminary;
import com.solum.xplain.xm.excmngmt.processipv.data.IpvTradeResultOverlay;
import com.solum.xplain.xm.excmngmt.rules.BreakTest;
import com.solum.xplain.xm.excmngmt.rules.filter.AssetFilter;
import com.solum.xplain.xm.excmngmt.rules.value.TestScope;
import com.solum.xplain.xm.excmngmt.rules.value.TestType;
import com.solum.xplain.xm.excmngmt.rulesipv.IpvBreakTest;
import com.solum.xplain.xm.excmngmt.rulesipv.TradeFilter;
import com.solum.xplain.xm.excmngmt.rulesipv.value.IpvProvidersType;
import com.solum.xplain.xm.excmngmt.rulesipv.value.IpvTestType;
import io.mongock.api.annotations.BeforeExecution;
import io.mongock.api.annotations.ChangeUnit;
import io.mongock.api.annotations.Execution;
import io.mongock.api.annotations.RollbackBeforeExecution;
import io.mongock.api.annotations.RollbackExecution;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.index.Index;

@AllArgsConstructor
@SuppressWarnings("squid:S1192") // Global constants should not be used
@ChangeUnit(order = "001", id = "1", author = "jevgenij")
public class ChangeUnit001 {
  private final MongoTemplate mongoTemplate;

  @BeforeExecution
  public void beforeExecution() {
    createPortfolioItemIndices();
    createCalculationIndices();
    createMdValueIndices();
    createMdkIndices();
    createSecMasterIndices();
    createComparisonIndices();
    createIpvDataIndices();
    createFixingIndices();
    createBreakTestIndices(BreakTest.class);
    createBreakTestIndices(IpvBreakTest.class);
    createXmResultIndices(InstrumentResultPreliminary.class);
    createXmResultIndices(InstrumentResultOverlay.class);
    createXmResultIndices(IpvTradeResultOverlay.class);
    createCompanyIndices();

    // Single indices
    mongoTemplate
        .indexOps(AuditEntryItem.class)
        .ensureIndex(new Index().named("auditEntryId").on("auditEntryId", Direction.ASC));

    mongoTemplate
        .indexOps(DateRangeSimulationErrorItem.class)
        .ensureIndex(new Index().named("simulationErrorItemId").on("simulationId", Direction.ASC));

    mongoTemplate
        .indexOps(AuditEntry.class)
        .ensureIndex(new Index().named("createdAt").on("createdAt", Direction.DESC));

    mongoTemplate
        .indexOps(Team.class)
        .ensureIndex(new Index().named("externalId_1").on("externalId", Direction.ASC).unique());

    mongoTemplate
        .indexOps(NonMtmPortfolioItem.class)
        .ensureIndex(
            new Index()
                .named("portfolio_version_state_idx")
                .on("portfolioId", Direction.ASC)
                .on("validFrom", Direction.DESC)
                .on("validities.recordFrom", Direction.DESC)
                .on("validities.recordTo", Direction.DESC)
                .on("validities.validTo", Direction.ASC)
                .on("state", Direction.ASC));

    mongoTemplate
        .indexOps(NonMtmValuationPortfolioItem.class)
        .ensureIndex(new Index().named("calculation_id").on("calculationResultId", Direction.ASC));
  }

  @Execution
  public void execute() {
    var providerExists =
        mongoTemplate
            .query(DataProvider.class)
            .matching(
                query(where(DataProvider.Fields.externalId).is(DataProvider.XPLAIN_PROVIDER_CODE)))
            .exists();
    if (providerExists) {
      return;
    }

    // Insert default provider
    var provider = new DataProvider();
    provider.setExternalId(DataProvider.XPLAIN_PROVIDER_CODE);
    provider.setName(DataProvider.XPLAIN_PROVIDER_CODE);
    provider.setTypes(Set.of(DataProviderType.VALUATION));
    mongoTemplate.insert(provider);

    mongoTemplate.insert(MdTaskDefaultTeams.empty());

    // Insert MD Preliminary test
    var mdNullTEst = BreakTest.newOf();
    mdNullTEst.setName("NULL");
    mdNullTEst.setType(TestType.NULL_VALUE);
    mdNullTEst.setScope(TestScope.PRELIMINARY);
    mdNullTEst.setAssetFilter(new AssetFilter());
    mdNullTEst.setSequence(1);
    mdNullTEst.enabled(true);
    mongoTemplate.insert(mdNullTEst);

    // Insert MD Batch test
    var batch = BreakTest.newOf();
    batch.setName("NULL (Batch)");
    batch.setType(TestType.NULL_VALUE);
    batch.setScope(TestScope.PRELIMINARY_BATCH);
    batch.setAssetFilter(new AssetFilter());
    batch.setSequence(1);
    batch.enabled(true);
    mongoTemplate.insert(batch);

    // Insert IPV test
    var nullTest = IpvBreakTest.newOf();
    nullTest.setName("NULL");
    nullTest.setType(IpvTestType.NULL_VALUE);
    nullTest.setProvidersTypes(List.of(IpvProvidersType.P1));
    nullTest.setTradeFilter(new TradeFilter());
    nullTest.setSequence(1);
    nullTest.enabled(true);
    mongoTemplate.insert(nullTest);
  }

  @RollbackExecution
  public void rollback() {
    // Rollback not needed
  }

  @RollbackBeforeExecution
  public void rollbackBefore() {
    // Rollback not needed
  }

  private void createCompanyIndices() {
    mongoTemplate
        .indexOps(CompanyLegalEntity.class)
        .ensureIndex(new Index().named("recordDate").on("recordDate", Direction.ASC));

    mongoTemplate
        .indexOps(CompanyLegalEntity.class)
        .ensureIndex(new Index().named("entityId").on("entityId", Direction.ASC));
  }

  private void createXmResultIndices(Class<?> resultClass) {
    mongoTemplate
        .indexOps(resultClass)
        .ensureIndex(
            new Index()
                .named("exceptionManagementId")
                .on("exceptionManagementResultId", Direction.ASC));

    mongoTemplate
        .indexOps(resultClass)
        .ensureIndex(
            new Index()
                .named("taskId_status_break")
                .on("taskId", Direction.ASC)
                .on("status", Direction.ASC)
                .on("hasBreak", Direction.ASC));
  }

  private void createFixingIndices() {
    mongoTemplate
        .indexOps(Fixing.class)
        .ensureIndex(
            new Index()
                .named("date_1_reference_1")
                .on("reference", Direction.ASC)
                .on("date", Direction.ASC));

    mongoTemplate
        .indexOps(Fixing.class)
        .ensureIndex(new Index().named("archivedAt_1").on("archivedAt", Direction.ASC));
  }

  private void createIpvDataIndices() {
    mongoTemplate
        .indexOps(IpvDataValue.class)
        .ensureIndex(
            new Index()
                .named("ipvDataProviderValue_idx")
                .on("groupId", Direction.ASC)
                .on("date", Direction.DESC)
                .on("archivedAt", Direction.ASC));
    mongoTemplate
        .indexOps(IpvDataValue.class)
        .ensureIndex(
            new Index()
                .named("archivedAt_1_resolved_1")
                .on("archivedAt", Direction.ASC)
                .on("resolved", Direction.ASC));
    mongoTemplate
        .indexOps(IpvDataValue.class)
        .ensureIndex(
            new Index().named("key_1_date_1").on("key", Direction.ASC).on("date", Direction.ASC));
    mongoTemplate
        .indexOps(IpvDataValue.class)
        .ensureIndex(
            new Index()
                .named("groupId_1_date_1_resolved_1_archivedAt_1")
                .on("groupId", Direction.ASC)
                .on("date", Direction.ASC)
                .on("resolved", Direction.ASC)
                .on("archivedAt", Direction.ASC));
  }

  private void createBreakTestIndices(Class<?> breakTestClass) {
    mongoTemplate
        .indexOps(breakTestClass)
        .ensureIndex(new Index().named("recordDate").on("recordDate", Direction.ASC));

    mongoTemplate
        .indexOps(breakTestClass)
        .ensureIndex(new Index().named("entityId").on("entityId", Direction.ASC));
  }

  private void createComparisonIndices() {
    mongoTemplate
        .indexOps(ComparisonResultItem.class)
        .ensureIndex(
            new Index()
                .named("comparisonResultItemCorrelationId")
                .on("correlationId", Direction.ASC));

    mongoTemplate
        .indexOps(ComparisonResult.class)
        .ensureIndex(
            new Index()
                .named("comparisonResultCreatedAt")
                .on("createdAt", Direction.ASC)
                .expire(30, TimeUnit.DAYS));

    mongoTemplate
        .indexOps(ComparisonResultItem.class)
        .ensureIndex(
            new Index()
                .named("comparisonResultItemCreatedAt")
                .on("createdAt", Direction.ASC)
                .expire(30, TimeUnit.DAYS));
  }

  private void createMdkIndices() {
    mongoTemplate
        .indexOps(MarketDataKey.class)
        .ensureIndex(
            new Index()
                .named("mdk_version_state_entityId_idx")
                .on("validFrom", Direction.DESC)
                .on("validities.recordFrom", Direction.DESC)
                .on("validities.recordTo", Direction.ASC)
                .on("validities.validTo", Direction.ASC)
                .on("state", Direction.ASC)
                .on("entityId", Direction.ASC));
    mongoTemplate
        .indexOps(MarketDataKey.class)
        .ensureIndex(
            new Index()
                .named("state_1_validFrom_1")
                .on("state", Direction.ASC)
                .on("validFrom", Direction.ASC));
    mongoTemplate
        .indexOps(MarketDataKey.class)
        .ensureIndex(new Index().named("entityId_1").on("entityId", Direction.ASC));
    mongoTemplate
        .indexOps(MarketDataKey.class)
        .ensureIndex(
            new Index()
                .named("assetGroup_1_state_1_validFrom_1")
                .on("assetGroup", Direction.ASC)
                .on("state", Direction.ASC)
                .on("validFrom", Direction.ASC));
    mongoTemplate
        .indexOps(MarketDataKey.class)
        .ensureIndex(
            new Index()
                .named("assetGroup_1_state_1_providerTicker_1_validFrom_1")
                .on("assetGroup", Direction.ASC)
                .on("state", Direction.ASC)
                .on("providerTicker", Direction.ASC)
                .on("validFrom", Direction.ASC));
  }

  private void createSecMasterIndices() {
    mongoTemplate
        .indexOps(SecMasterTradeReadEntity.class)
        .ensureIndex(new Index().named("entityId_1").on("entityId", Direction.ASC));

    mongoTemplate
        .indexOps(SecMasterTradeReadEntity.class)
        .ensureIndex(new Index().named("externalTradeId_1").on("externalTradeId", Direction.ASC));

    mongoTemplate
        .indexOps(SecMasterTradeReadEntity.class)
        .ensureIndex(
            new Index()
                .named("validItems_idx")
                .on("validFrom", Direction.DESC)
                .on("validities.validTo", Direction.ASC)
                .on("validities.recordFrom", Direction.DESC)
                .on("validities.recordTo", Direction.ASC)
                .on("state", Direction.ASC));
  }

  private void createMdValueIndices() {
    mongoTemplate
        .indexOps(MarketDataValue.class)
        .ensureIndex(
            new Index()
                .named("market_data_value_idx")
                .on("groupId", Direction.DESC)
                .on("date", Direction.ASC)
                .on("provider", Direction.ASC)
                .on("ticker", Direction.ASC));
    mongoTemplate
        .indexOps(MarketDataValue.class)
        .ensureIndex(
            new Index()
                .named("groupId_1_date_1_archivedAt_1")
                .on("groupId", Direction.ASC)
                .on("date", Direction.ASC)
                .on("archivedAt", Direction.ASC));
  }

  private void createCalculationIndices() {
    mongoTemplate
        .indexOps(CalculationPortfolioItem.class)
        .ensureIndex(
            new Index()
                .named("calculation_status")
                .on("calculationResultId", Direction.ASC)
                .on("valuationStatus", Direction.ASC));

    mongoTemplate
        .indexOps(CalculationResult.class)
        .ensureIndex(
            new Index()
                .named("status_portfolio.portfolioId")
                .on("calculationResultStatus", Direction.ASC)
                .on("portfolioId", Direction.ASC));
  }

  private void createPortfolioItemIndices() {
    // Read model
    mongoTemplate
        .indexOps(PortfolioItem.class)
        .ensureIndex(new Index().named("portfolioId_1").on("portfolioId", Direction.ASC));

    mongoTemplate
        .indexOps(PortfolioItem.class)
        .ensureIndex(new Index().named("entityId_1").on("entityId", Direction.ASC));

    mongoTemplate
        .indexOps(PortfolioItem.class)
        .ensureIndex(new Index().named("externalTradeId_1").on("externalTradeId", Direction.ASC));
    mongoTemplate
        .indexOps(PortfolioItem.class)
        .ensureIndex(
            new Index()
                .named("referenceTradeId_1")
                .on("allocationTradeDetails.referenceTradeId", Direction.ASC));
    mongoTemplate
        .indexOps(PortfolioItem.class)
        .ensureIndex(
            new Index()
                .named("portfolio_version_state_idx")
                .on("portfolioId", Direction.ASC)
                .on("validFrom", Direction.DESC)
                .on("validities.recordFrom", Direction.DESC)
                .on("validities.recordTo", Direction.DESC)
                .on("validities.validTo", Direction.ASC)
                .on("state", Direction.ASC));

    // Write model
    mongoTemplate
        .indexOps(PortfolioItemEntity.class)
        .ensureIndex(new Index().named("semanticId_1").on("semanticId", Direction.ASC));

    mongoTemplate
        .indexOps(PortfolioItemEntity.class)
        .ensureIndex(new Index().named("portfolioId_1").on("portfolioId", Direction.ASC));

    mongoTemplate
        .indexOps(PortfolioItemEntity.class)
        .ensureIndex(
            new Index()
                .named("referenceTradeId_1")
                .on("versions.value.allocationTradeDetails.referenceTradeId", Direction.ASC));
  }
}
