package com.solum.xplain.core.curvegroup.curve.csv.node

import static com.solum.xplain.core.common.csv.DuplicateAction.ERROR
import static com.solum.xplain.core.curvegroup.curve.entity.CurveBuilder.curve
import static com.solum.xplain.core.error.Error.IMPORT_ERROR
import static com.solum.xplain.core.error.Error.PARSING_ERROR

import com.google.common.io.ByteStreams
import com.solum.xplain.core.common.csv.NamedList
import com.solum.xplain.core.curvegroup.curve.classifier.CurveType
import com.solum.xplain.core.curvegroup.curve.value.CurveNodeForm
import com.solum.xplain.extensions.product.ExtendedIborContractSpecs
import spock.lang.Specification

class CurveNodesCsvLoaderTest extends Specification {

  def static CURVES = [
    curve().with {
      setName("EUR/AUD")
      setCurveType(CurveType.XCCY)
      return it
    },
    curve().with {
      setName("EUR/USD")
      setCurveType(CurveType.XCCY)
      return it
    },
    curve().with {
      setName("GBP/USD")
      setCurveType(CurveType.XCCY)
      return it
    },
    curve().with {
      setName("GBP/USD")
      setCurveType(CurveType.XCCY)
      return it
    },
    curve().with {
      setName("EUR/USD")
      setCurveType(CurveType.XCCY)
      return it
    },
    curve().with {
      setName("BRL CDI")
      setCurveType(CurveType.IR_INDEX)
      return it
    },
    curve().with {
      setName("BRL CDI Offshore")
      setCurveType(CurveType.IR_INDEX)
      return it
    },
    curve().with {
      setName("INR OMIBOR")
      setCurveType(CurveType.IR_INDEX)
      return it
    },
    curve().with {
      setName("USD SOFR")
      setCurveType(CurveType.IR_INDEX)
      return it
    },
    curve().with {
      setName("AUD 3M")
      setCurveType(CurveType.IR_INDEX)
      return it
    },
    curve().with {
      setName("EUR 6M")
      setCurveType(CurveType.INDEX_BASIS)
      return it
    },
    curve().with {
      setName("GB RPI")
      setCurveType(CurveType.INFLATION_INDEX)
      return it
    },
    curve().with {
      setName("AUD 3M")
      setCurveType(CurveType.INDEX_BASIS)
      return it
    },
    curve().with {
      setName("NOK 6M")
      setCurveType(CurveType.IR_INDEX)
      return it
    },
  ]

  CurveNodesCsvLoader loader = new CurveNodesCsvLoader()
  ExtendedIborContractSpecs extendedIborContractSpecs = new ExtendedIborContractSpecs()

  def "should return correctly parsed CSV rows"() {
    setup:
    def bytes = loadResource("CurveNodes.csv")

    when:
    def result = loader.parse(bytes, CURVES, ERROR)

    then:
    result.errors.size() == 0
    result.warnings.size() == 0
    result.namedLists.size() == 10

    Map<String, NamedList<CurveNodeForm>> nodesByNames = result.namedLists.collectEntries {
      [(it.name): it]
    }

    nodesByNames.containsKey("EUR/AUD")
    nodesByNames.get("EUR/AUD").name == "EUR/AUD"
    nodesByNames.get("EUR/AUD").items.size() == 2
    nodesByNames.get("EUR/AUD").items[0].period == "1D"
    nodesByNames.get("EUR/AUD").items[1].period == "2D"
    nodesByNames.get("EUR/AUD").items[0].type == "XCCYIborIborSwap"
    nodesByNames.get("EUR/AUD").items[1].type == "XCCYIborIborSwap"
    nodesByNames.get("EUR/AUD").items[0].convention == "AUD-BBSW-3M-EUR-EURIBOR-3M"
    nodesByNames.get("EUR/AUD").items[1].convention == "AUD-BBSW-3M-EUR-EURIBOR-3M"
    nodesByNames.get("EUR/AUD").items[0].fraSettlement == null
    nodesByNames.get("EUR/AUD").items[1].fraSettlement == null
    nodesByNames.get("EUR/AUD").items[0].serialFuture == null
    nodesByNames.get("EUR/AUD").items[1].serialFuture == null

    nodesByNames.containsKey("GBP/USD")
    nodesByNames.get("GBP/USD").name == "GBP/USD"
    nodesByNames.get("GBP/USD").items.size() == 3
    nodesByNames.get("GBP/USD").items[0].period == "1Y"
    nodesByNames.get("GBP/USD").items[1].period == "2D"
    nodesByNames.get("GBP/USD").items[2].period == "TN"

    nodesByNames.get("GBP/USD").items[0].type == "FxSwap"
    nodesByNames.get("GBP/USD").items[1].type == "FxSwap"
    nodesByNames.get("GBP/USD").items[2].type == "FxSwap"

    nodesByNames.get("GBP/USD").items[0].convention == "GBP/USD"
    nodesByNames.get("GBP/USD").items[1].convention == "GBP/USD"
    nodesByNames.get("GBP/USD").items[2].convention == "GBP/USD"

    nodesByNames.get("GBP/USD").items[0].fraSettlement == null
    nodesByNames.get("GBP/USD").items[1].fraSettlement == null
    nodesByNames.get("GBP/USD").items[2].fraSettlement == null

    nodesByNames.get("GBP/USD").items[0].serialFuture == null
    nodesByNames.get("GBP/USD").items[1].serialFuture == null
    nodesByNames.get("GBP/USD").items[2].serialFuture == null

    nodesByNames.containsKey("BRL CDI")
    nodesByNames.get("BRL CDI").name == "BRL CDI"
    nodesByNames.get("BRL CDI").items.size() == 1
    nodesByNames.get("BRL CDI").items[0].period == "5Q"
    nodesByNames.get("BRL CDI").items[0].type == "FixedOvernightSwap"
    nodesByNames.get("BRL CDI").items[0].convention == "BRL-FIXED-TERM-CDI-OIS"
    nodesByNames.get("BRL CDI").items[0].fraSettlement == null
    nodesByNames.get("BRL CDI").items[0].serialFuture == null

    nodesByNames.containsKey("BRL CDI Offshore")
    nodesByNames.get("BRL CDI Offshore").name == "BRL CDI Offshore"
    nodesByNames.get("BRL CDI Offshore").items.size() == 1
    nodesByNames.get("BRL CDI Offshore").items[0].period == "5Q"
    nodesByNames.get("BRL CDI Offshore").items[0].type == "FixedOvernightSwap"
    nodesByNames.get("BRL CDI Offshore").items[0].convention == "BRL-FIXED-TERM-CDI-OIS-OFFSHORE"
    nodesByNames.get("BRL CDI Offshore").items[0].fraSettlement == null
    nodesByNames.get("BRL CDI Offshore").items[0].serialFuture == null

    nodesByNames.containsKey("USD SOFR")
    nodesByNames.get("USD SOFR").name == "USD SOFR"
    nodesByNames.get("USD SOFR").items.size() == 2
    nodesByNames.get("USD SOFR").items[0].period == "1D"
    nodesByNames.get("USD SOFR").items[0].type == "FixedOvernightSwap"
    nodesByNames.get("USD SOFR").items[0].convention == "USD-FIXED-TERM-SOFR-OIS"
    nodesByNames.get("USD SOFR").items[0].fraSettlement == null
    nodesByNames.get("USD SOFR").items[0].serialFuture == null
    nodesByNames.get("USD SOFR").items[1].period == "2D"
    nodesByNames.get("USD SOFR").items[1].type == "FixedOvernightSwap"
    nodesByNames.get("USD SOFR").items[1].convention == "USD-FIXED-SHORTTERM-SOFR-OIS"
    nodesByNames.get("USD SOFR").items[1].fraSettlement == null
    nodesByNames.get("USD SOFR").items[1].serialFuture == null

    nodesByNames.containsKey("EUR 6M")
    nodesByNames.get("EUR 6M").name == "EUR 6M"
    nodesByNames.get("EUR 6M").items.size() == 1
    nodesByNames.get("EUR 6M").items[0].period == "1D"
    nodesByNames.get("EUR 6M").items[0].type == "IborIborSwap"
    nodesByNames.get("EUR 6M").items[0].convention == "EUR-EURIBOR-3M-EURIBOR-6M"
    nodesByNames.get("EUR 6M").items[0].fraSettlement == null
    nodesByNames.get("EUR 6M").items[0].serialFuture == null

    nodesByNames.containsKey("EUR/USD")
    nodesByNames.get("EUR/USD").name == "EUR/USD"
    nodesByNames.get("EUR/USD").items.size() == 1
    nodesByNames.get("EUR/USD").items[0].period == "1Y"
    nodesByNames.get("EUR/USD").items[0].type == "XCCYOvernightOvernightSwap"
    nodesByNames.get("EUR/USD").items[0].convention == "EUR-ESTR-USD-SOFR"
    nodesByNames.get("EUR/USD").items[0].fraSettlement == null
    nodesByNames.get("EUR/USD").items[0].serialFuture == null

    nodesByNames.containsKey("GB RPI")
    nodesByNames.get("GB RPI").name == "GB RPI"
    nodesByNames.get("GB RPI").items.size() == 3
    nodesByNames.get("GB RPI").items[0].period == "5Y"
    nodesByNames.get("GB RPI").items[0].type == "FixedInflationSwap"
    nodesByNames.get("GB RPI").items[0].convention == "GBP-FIXED-ZC-GB-RPI"
    nodesByNames.get("GB RPI").items[0].fraSettlement == null
    nodesByNames.get("GB RPI").items[0].serialFuture == null
    nodesByNames.get("GB RPI").items[1].period == "6Y"
    nodesByNames.get("GB RPI").items[1].type == "FixedInflationSwap"
    nodesByNames.get("GB RPI").items[1].convention == "GBP-FIXED-ZC-GB-RPI-CLEARED"
    nodesByNames.get("GB RPI").items[1].fraSettlement == null
    nodesByNames.get("GB RPI").items[1].serialFuture == null
    nodesByNames.get("GB RPI").items[2].period == "6M"
    nodesByNames.get("GB RPI").items[2].type == "FixedInflationSwap"
    nodesByNames.get("GB RPI").items[2].convention == "GBP-FIXED-ZC-GB-RPI-CLEARED-30360"
    nodesByNames.get("GB RPI").items[2].fraSettlement == null
    nodesByNames.get("GB RPI").items[2].serialFuture == null

    nodesByNames.containsKey("AUD 3M")
    nodesByNames.get("AUD 3M").name == "AUD 3M"
    nodesByNames.get("AUD 3M").items.size() == 3
    nodesByNames.get("AUD 3M").items[0].period == "3M"
    nodesByNames.get("AUD 3M").items[0].type == "TermDeposit"
    nodesByNames.get("AUD 3M").items[0].convention == "AUD-Deposit-T0"
    nodesByNames.get("AUD 3M").items[0].fraSettlement == null
    nodesByNames.get("AUD 3M").items[0].serialFuture == null
    nodesByNames.get("AUD 3M").items[1].period == "3M"
    nodesByNames.get("AUD 3M").items[1].type == "TermDeposit"
    nodesByNames.get("AUD 3M").items[1].convention == "AUD-Deposit-T1"
    nodesByNames.get("AUD 3M").items[1].fraSettlement == null
    nodesByNames.get("AUD 3M").items[1].serialFuture == null
    nodesByNames.get("AUD 3M").items[2].period == "3M"
    nodesByNames.get("AUD 3M").items[2].type == "TermDeposit"
    nodesByNames.get("AUD 3M").items[2].convention == "AUD-Deposit-T2"
    nodesByNames.get("AUD 3M").items[2].fraSettlement == null
    nodesByNames.get("AUD 3M").items[2].serialFuture == null

    nodesByNames.containsKey("INR OMIBOR")
    nodesByNames.get("INR OMIBOR").name == "INR OMIBOR"
    nodesByNames.get("INR OMIBOR").items.size() == 2
    nodesByNames.get("INR OMIBOR").items[0].period == "ON"
    nodesByNames.get("INR OMIBOR").items[0].type == "TermDeposit"
    nodesByNames.get("INR OMIBOR").items[0].convention == "INR-Deposit-T0"
    nodesByNames.get("INR OMIBOR").items[0].fraSettlement == null
    nodesByNames.get("INR OMIBOR").items[0].serialFuture == null
    nodesByNames.get("INR OMIBOR").items[1].period == "1D"
    nodesByNames.get("INR OMIBOR").items[1].type == "FixedOvernightSwap"
    nodesByNames.get("INR OMIBOR").items[1].convention == "INR-FIXED-TERM-OMIBOR-OIS"
    nodesByNames.get("INR OMIBOR").items[1].fraSettlement == null
    nodesByNames.get("INR OMIBOR").items[1].serialFuture == null
  }

  def "should parse IMM FRA nodes"() {
    setup:
    def bytes = """\
        Curve Name,Tenor,Instrument Type,Convention,FRA Settlement,Serial Future
        NOK 6M,,IMMForwardRateAgreement,NOK-NIBOR-6M-IMM-FRA,,2D+12
        """.stripIndent().bytes

    when:
    def result = loader.parse(bytes, CURVES, ERROR)

    then:
    result.errors.size() == 0
    result.warnings.size() == 0
    result.namedLists.size() == 1

    and:
    result.namedLists[0].items.size() == 1
    result.namedLists[0].items[0].period == "6M"
    result.namedLists[0].items[0].convention == "NOK-NIBOR-6M-IMM-FRA"
    result.namedLists[0].items[0].serialFuture == "2D+12"
    result.namedLists[0].items[0].type == "IMMForwardRateAgreement"
  }

  def "should throw error if missing header"() {
    setup:
    def bytes = """\
        GBP/USD,1D,FxSwap,GBP/USD
        """.stripIndent().bytes

    when:
    def result = loader.parse(bytes, CURVES, ERROR)

    then:
    result.errors.size() == 1
    result.warnings.size() == 0
    result.namedLists.size() == 0

    def errors = result.errors
    errors[0].reason == PARSING_ERROR
    errors[0].description.startsWith("Missing header:")
  }

  def "should return parse errors"() {
    setup:
    def bytes = loadResource("CurveNodesInvalid.csv")

    when:
    def result = loader.parse(bytes, CURVES, ERROR)

    then:
    result.errors.size() == 11
    result.warnings.size() == 0
    result.namedLists.size() == 0

    def errors = result.errors
    errors[0].description == "Error parsing line 8: Curve Name field missing or empty"
    errors[1].description == "Error parsing line 11: No value was found for 'Tenor'"
    errors[2].description == "Error parsing line 12: Unsupported value: YY"
    errors[3].description == "Error parsing line 7: Unsupported value: ON"
    errors[4].description == "Error parsing line 10: Unsupported value: XX"
    errors[5].description.startsWith("Error parsing line 2: Unsupported value: EUR-EURIBOR-3M-USD-LIBOR-3M1. Supported values: ")
    errors[6].description == "Error parsing line 4: Unsupported value: 1K"
    errors[7].description.startsWith("Error parsing line 5: Unsupported value: FxSwap1. Supported values:")
    errors[8].description == "Error parsing line 9: No value was found for 'Tenor'"
    errors[9].description == "Curve GBP/USD requires a TN node. Please add a TN node to the curve or remove ON node."
    errors[10].description == "Unsupported Node type for EUR 6M Curve found. Supported types are: [IborFixingDeposit,IborIborSwap,ForwardRateAgreement]"
  }

  def "should check unique node name"() {
    setup:
    def bytes = loadResource("CurveNodesDuplicate.csv")

    when:
    def result = loader.parse(bytes, CURVES, ERROR)

    then:
    result.errors.size() == 3
    result.warnings.size() == 0
    result.namedLists.size() == 0

    def errors = result.errors
    errors[0].description == "Error parsing line 5: Duplicate found: GBP/USD ON"
    errors[1].reason == PARSING_ERROR
    errors[1].description == "Error parsing line 3: Duplicate found: EUR/USD 1D"
    errors[2].reason == PARSING_ERROR
    errors[2].description == "Error parsing line 7: Duplicate found: NZD 3M NZD-Deposit-T0"
  }

  def "should validate curve nodes period by type"() {
    setup:
    def bytes = loadResource("CurveNodesInvalidPeriod.csv")

    when:
    def result = loader.parse(bytes, CURVES, ERROR)

    then:
    result.errors.size() == 11
    result.warnings.size() == 0
    result.namedLists.size() == 0
    result.errors[0].description == "Error parsing line 10: Unsupported value: SN"
    result.errors[1].description == "Error parsing line 11: Unsupported value: SW"
    result.errors[2].description == "Error parsing line 7: Unsupported value: SN"
    result.errors[3].description == "Error parsing line 6: No value was found for 'Serial Future'"
    result.errors[4].description == "Error parsing line 4: Unsupported value: 1MX2M"
    result.errors[5].description == "Error parsing line 5: Unsupported value: 1M+1"
    result.errors[6].description == "Error parsing line 12: Unsupported value: SN"
    result.errors[7].description == "Error parsing line 13: Unsupported value: SW"
    result.errors[8].description == "Error parsing line 14: Unsupported value: TN"
    result.errors[9].description == "Error parsing line 8: Unsupported value: 6W"
    result.errors[10].description == "Error parsing line 9: Unsupported value: 365D"
  }

  def "should return left when curve name function returns error for all nodes"() {
    setup:
    def bytes = """\
        Curve Name,Tenor,Instrument Type,Convention,FRA Settlement,Serial Future
        EUR/USD,1M,XCCYIborIborSwap,EUR-EURIBOR-3M-USD-LIBOR-3M
        EUR/USD,1D,XCCYIborIborSwap,EUR-EURIBOR-3M-USD-LIBOR-3M
        EUR/USD,2D,XCCYIborIborSwap,EUR-EURIBOR-3M-USD-LIBOR-3M
        """.stripIndent().bytes

    when:
    def result = loader.parse(bytes, [], ERROR)

    then:
    result.errors.size() == 1
    result.warnings.size() == 0
    result.namedLists.size() == 0

    def errors = result.errors
    errors[0].reason == IMPORT_ERROR
  }

  byte[] loadResource(String fileName) {
    return ByteStreams.toByteArray(getClass().getResourceAsStream("/curvegroup/curve/csv/node/" + fileName))
  }
}
