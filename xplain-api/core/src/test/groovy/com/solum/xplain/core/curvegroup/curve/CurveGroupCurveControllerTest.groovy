package com.solum.xplain.core.curvegroup.curve

import static com.opengamma.strata.product.swap.type.FixedIborSwapConventions.EUR_FIXED_1Y_EURIBOR_3M
import static com.solum.xplain.core.classifiers.CurveNodeTypes.FIXED_IBOR_SWAP_NODE
import static com.solum.xplain.core.classifiers.CurveNodeTypes.FIXED_INFLATION_SWAP_NODE
import static com.solum.xplain.core.classifiers.CurveNodeTypes.FIXED_OVERNIGHT_SWAP_NODE
import static com.solum.xplain.core.classifiers.CurveNodeTypes.FRA_NODE
import static com.solum.xplain.core.classifiers.CurveNodeTypes.FX_SWAP_NODE
import static com.solum.xplain.core.classifiers.CurveNodeTypes.IBOR_FUTURE_NODE
import static com.solum.xplain.core.classifiers.CurveNodeTypes.IMM_FRA_NODE
import static com.solum.xplain.core.classifiers.CurveNodeTypes.TERM_DEPOSIT_NODE
import static com.solum.xplain.core.common.csv.DuplicateAction.ERROR
import static com.solum.xplain.core.curvegroup.curve.CurveGroupCurveRepository.uniqueEntityCriteria
import static com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceType.ASK_PRICE
import static io.atlassian.fugue.Either.right
import static java.time.LocalDate.parse
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.multipart
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status

import com.fasterxml.jackson.databind.ObjectMapper
import com.opengamma.strata.market.curve.CurveNodeClashAction
import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.common.RequestPathVariablesSupport
import com.solum.xplain.core.common.csv.FileResponseEntity
import com.solum.xplain.core.common.validation.UniqueEntitySupport
import com.solum.xplain.core.common.value.NewVersionFormV2
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.curvegroup.curve.dto.CalibratedCurvesOptions
import com.solum.xplain.core.curvegroup.curve.dto.GetCalibratedCurvesRequest
import com.solum.xplain.core.curvegroup.curve.entity.Curve
import com.solum.xplain.core.curvegroup.curve.value.CurveForm
import com.solum.xplain.core.curvemarket.CurveConfigMarketStateForm
import com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceRequirementsForm
import com.solum.xplain.core.portfolio.value.CalculationDiscountingType
import com.solum.xplain.core.portfolio.value.CalculationStrippingType
import com.solum.xplain.core.test.MockMvcConfiguration
import com.solum.xplain.extensions.immfra.ExtendedImmutableImmFraConventions
import com.solum.xplain.extensions.product.ExtendedTermDepositConventions
import java.time.LocalDate
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.core.io.ByteArrayResource
import org.springframework.data.domain.Sort
import org.springframework.http.MediaType
import org.springframework.mock.web.MockMultipartFile
import org.springframework.security.test.context.support.WithMockUser
import org.springframework.test.web.servlet.MockMvc
import org.springframework.util.LinkedMultiValueMap
import spock.lang.Specification
import spock.lang.Unroll

@WebMvcTest(controllers = [CurveGroupCurveController])
@MockMvcConfiguration
class CurveGroupCurveControllerTest extends Specification {

  @SpringBean
  private CurveGroupCurveService service = Mock()
  @SpringBean
  private CurveGroupCurveExportService exportService = Mock()
  @SpringBean
  private CurveGroupCurveImportService importService = Mock()

  @SpringBean
  private CurveGroupCurveRepository repository = Mock()
  @SpringBean
  private RequestPathVariablesSupport pathVariablesSupport = new RequestPathVariablesSupport()
  @SpringBean
  private UniqueEntitySupport uniqueSupport = Mock()

  @Autowired
  private MockMvc mockMvc
  @Autowired
  private ObjectMapper objectMapper


  @Unroll
  @WithMockUser
  def "should perform validation when inserting with response #code #responseBody and form (#form)"() {
    setup:
    service.createCurve("groupId", _ as CurveForm) >> right(EntityId.entityId("1"))
    uniqueSupport.existsByCriteria(_ as LocalDate, uniqueEntityCriteria("groupId", "USD 3M"), Curve.class) >> true
    def json = objectMapper.writeValueAsString(form)
    def results = mockMvc.perform(post("/curve-group/{groupId}/curves", "groupId")
    .with(csrf())
    .content(json)
    .contentType(MediaType.APPLICATION_JSON))
    .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    form                                                                               | code | responseBody
    curveForm()                                                                        | 200  | "id"
    curveForm {
      b -> b.put("name", null)
    }                                             | 412  | "NotEmpty.curveForm.name"
    curveForm {
      b -> b.put("name", "Aaa")
    }                                            | 412  | "Invalid curve name : Aaa for curve type: IR_INDEX"
    curveForm {
      b -> b.put("name", "AUD 3M")
    }                                         | 200  | "id"
    curveForm {
      b -> b.put("name", "AUD/USD")
    }                                        | 412  | "Invalid curve name : AUD/USD for curve type: IR_INDEX"
    curveForm {
      b -> b.put("name", "USD 3M")
    }                                         | 412  | "Curve USD 3M already exists at 1970-01-01 date"
    curveForm {
      b -> b.put("curveType", null)
    }                                        | 412  | "NotNull.curveForm.curveType"
    curveForm {
      b -> b.put("curveType", "InvalidType")
    }                               | 412  | "ValidStringSet.curveType"
    curveForm {
      b -> b.put("interpolator", null)
    }                                     | 412  | "NotNull.curveForm.interpolator"
    curveForm {
      b -> b.put("interpolator", "LogLinearDiscountFactor")
    }                | 412  | "ValidStringSet.interpolator"
    curveForm {
      b -> b.put("extrapolatorLeft", null)
    }                                 | 412  | "NotNull.curveForm.extrapolatorLeft"
    curveForm {
      b -> b.put("extrapolatorLeft", "LogLinearDiscountFactor")
    }            | 412  | "ValidStringSet.curveForm.extrapolatorLeft"
    curveForm {
      b -> b.put("extrapolatorRight", null)
    }                                | 412  | "NotNull.curveForm.extrapolatorRight"
    curveForm {
      b -> b.put("minGap", "1")
    }                                            | 412  | "ValidStringSet.curveForm.minGap"
    curveForm {
      b -> b.put("clashAction", "1")
    }                                       | 412  | "ValidStringSet.curveForm.clashAction"
    curveForm {
      b -> b.put("yInterpolationMethod", "PriceIndex")
    }                     | 412  | "ValidStringSet.curveForm.yInterpolationMethod"
    curveForm {
      b -> b.put("nodes", [nodeForm(), nodeForm()])
    }                        | 412  | "UniqueCurveNodes.curveForm.nodes"
    curveForm {
      b -> b.put("nodes", [termDepositNodeForm(), termDepositNodeForm()])
    }  | 412  | "UniqueCurveNodes.curveForm.nodes"
    curveForm {
      b -> b.put("nodes", [nodeForm(), fraNodeForm()])
    }                     | 412  | "UniqueCurveNodes.curveForm.nodes"
    curveForm {
      b -> {
        b.put("yInterpolationMethod", "DiscountFactor")
        b.put("interpolator", "LogLinearDiscountFactor")
      }
    }                                                                                  | 200  | "id"
    curveForm {
      b -> {
        b.put("yInterpolationMethod", "DiscountFactor")
        b.put("interpolator", "LogLinearDiscountFactor")
        b.put("extrapolatorLeft", "LogLinear")
      }
    }                                                                                  | 412  | "ValidStringSet.curveForm.extrapolatorLeft"
    curveForm {
      b -> {
        b.put("yInterpolationMethod", "DiscountFactor")
        b.put("interpolator", "LogLinearDiscountFactor")
        b.put("extrapolatorRight", "LogLinear")
      }
    }                                                                                  | 412  | "ValidStringSet.curveForm.extrapolatorRight"

    curveInflationForm {
      b -> b.put("minGap", "1D")
    }                                  | 412  | "Null.curveForm.minGap"
    curveInflationForm {
      b -> b.put("clashAction", "DropThis")
    }                       | 412  | "Null.curveForm.clashAction"
    curveInflationForm {
      b -> b.put("yInterpolationMethod", "ZeroRate")
    }              | 412  | "ValidStringSet.curveForm.yInterpolationMethod"

    curveFxForm {
      b -> b.put("nodes", [fxNodeForm()])
    }                                | 200  | "id"
    nok3MForm {
      b -> b.put("nodes", [immFraNodeForm()])
    }                              | 200  | "id"
    nok3MForm {
      b -> b.put("nodes", [immFraNodeForm(c -> c.remove("serialFuture"))])
    } | 412  | "NotEmpty.nodes[0].serialFuture"
    nok3MForm {
      b -> b.put("nodes", [immFraNodeForm(c -> c.serialFuture = "2d,1")])
    } | 412  | "ValidStringSet.curveForm.nodes[0].serialFuture"
    curveFxForm {
      b -> b.put("nodes", [fxMarketTenorNodeForm(c -> c.period = "TN")])
    } | 200  | "id"
    curveFxForm {
      b -> b.put("nodes", [fxMarketTenorNodeForm()])
    }                     | 412  | "Curve EUR/USD requires a TN node. Please add a TN node to the curve or remove ON node."
    curveForm {
      b -> {
        b.put("yInterpolationMethod", "DiscountFactor")
        b.put("interpolator", "LogLinearDiscountFactor")
        b.put("extrapolatorLeft", "Linear")
        b.put("extrapolatorRight", "Linear")
      }
    }                                                                                  | 200  | "id"
  }

  @Unroll
  @WithMockUser
  def "should perform node form (#form) validation with response #code #responseBody"() {
    setup:
    service.createCurve("groupId", _ as CurveForm) >> right(EntityId.entityId("1"))
    def curveForms = curveForm { b -> b.put("nodes", [form]) }
    def json = objectMapper.writeValueAsString(curveForms)
    def results = mockMvc.perform(post("/curve-group/{groupId}/curves", "groupId")
    .with(csrf())
    .content(json)
    .contentType(MediaType.APPLICATION_JSON))
    .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    form                                                                                                                                    | code | responseBody
    nodeForm()                                                                                                                              | 200  | "id"
    nodeForm({ c -> c.putAll(["period": null, "type": FRA_NODE, convention: "AED-EIBOR-3M", fraSettlement: "1M"]) })                        | 200  | "id"
    nodeForm({ c -> c.putAll(["period": null, "type": IBOR_FUTURE_NODE, convention: "GBP-LIBOR-3M-Quarterly-IMM", serialFuture: "2D+1"]) }) | 200  | "id"
    nodeForm({ c -> c.putAll(["period": "1D", "type": FIXED_OVERNIGHT_SWAP_NODE, convention: "EUR-FIXED-SHORTTERM-ESTR-OIS"]) })            | 200  | "id"
    nodeForm({ c -> c.putAll(["period": "1D", "type": FIXED_OVERNIGHT_SWAP_NODE, convention: "BRL-FIXED-TERM-CDI-OIS"]) })                  | 200  | "id"
    nodeForm({ c -> c.putAll(["period": "1Q", "type": FIXED_OVERNIGHT_SWAP_NODE, convention: "BRL-FIXED-TERM-CDI-OIS-OFFSHORE"]) })         | 200  | "id"
    nodeForm({ c -> c.putAll(["period": "1Y", "type": FIXED_INFLATION_SWAP_NODE, convention: "CHF-FIXED-ZC-CH-CPI"]) })                     | 200  | "id"
    nodeForm({ c -> c.putAll(["period": "1Y", "type": FIXED_INFLATION_SWAP_NODE, convention: "CHF-FIXED-ZC-CH-CPI-30360"]) })               | 200  | "id"
    nodeForm({ c -> c.putAll(["period": "6M", "type": FIXED_INFLATION_SWAP_NODE, convention: "CHF-FIXED-ZC-CH-CPI"]) })                     | 200  | "id"
    nodeForm({ c -> c.putAll(["period": "6M", "type": FIXED_INFLATION_SWAP_NODE, convention: "CHF-FIXED-ZC-CH-CPI-30360"]) })               | 200  | "id"
    nodeForm({ c -> c.putAll(["period": "ON", "type": FIXED_OVERNIGHT_SWAP_NODE, convention: "INR-FIXED-TERM-OMIBOR-OIS"]) })               | 412  | "Invalid period. Only D/W/M/Y (1D, 2Y, 3M, 4W) allowed."
    nodeForm({ c -> c.putAll(["period": "1D", "type": FIXED_OVERNIGHT_SWAP_NODE, convention: "INR-FIXED-TERM-OMIBOR-OIS"]) })               | 200  | "id"
    nodeForm({ c -> c.putAll(["period": "1D", "type": FX_SWAP_NODE, convention: "EUR/USD"]) })                                              | 200  | "id"
    nodeForm({ c -> c.putAll(["period": "ON", "type": FX_SWAP_NODE, convention: "EUR/USD"]) })                                              | 200  | "id"
    nodeForm({ c -> c.putAll(["period": "SN", "type": FX_SWAP_NODE, convention: "EUR/USD"]) })                                              | 412  | "id"
    nodeForm({ c -> c.putAll(["period": "365D", "type": FIXED_INFLATION_SWAP_NODE, convention: "CHF-FIXED-ZC-CH-CPI"]) })                   | 412  | "Invalid period. Only M/Y allowed (2Y, 3M)."
    nodeForm({ c -> c.putAll(["period": null, "type": FRA_NODE, convention: "AED-EIBOR-3M"]) })                                             | 412  | "NotEmpty.curveForm.nodes[0].fraSettlement"
    nodeForm({ c -> c.putAll(["period": null, "type": IBOR_FUTURE_NODE, convention: "GBP-LIBOR-3M-Quarterly-IMM"]) })                       | 412  | "NotEmpty.curveForm.nodes[0].serialFuture"
    nodeForm({ c -> c.putAll(["period": "2MX2M"]) })                                                                                        | 412  | "Invalid period. Only D/W/M/Y (1D, 2Y, 3M, 4W) allowed."
    nodeForm({ c -> c.putAll(["period": "2M+1"]) })                                                                                         | 412  | "Invalid period. Only D/W/M/Y (1D, 2Y, 3M, 4W) allowed."
    nodeForm({ c -> c.putAll(["period": "2W", type: FIXED_OVERNIGHT_SWAP_NODE, convention: "BRL-FIXED-TERM-CDI-OIS"]) })                    | 412  | "Invalid period. Only M/Q/S/Y (2M, 3Q, 4S, 5Y) and 1D allowed."
    nodeForm({ c -> c.putAll(["period": "2W", type: FIXED_OVERNIGHT_SWAP_NODE, convention: "BRL-FIXED-TERM-CDI-OIS-OFFSHORE"]) })           | 412  | "Invalid period. Only M/Q/S/Y (2M, 3Q, 4S, 5Y) and 1D allowed."
    nodeForm({ c -> c.putAll(["period": "2W", type: TERM_DEPOSIT_NODE, convention: "AUD-DEPOSIT-T1"]) })                                    | 412  | "Null.curveForm.nodes[0].period"
    nodeForm({ c -> c.putAll(["period": null, type: TERM_DEPOSIT_NODE, convention: "INR-Deposit-T0"]) })                                    | 200  | "id"
    nodeForm({ c -> c.putAll(["period": null, type: TERM_DEPOSIT_NODE, convention: "NZD-Deposit-T0"]) })                                    | 200  | "id"
    nodeForm({ c -> c.putAll(["convention": null]) })                                                                                       | 412  | "NotEmpty.curveForm.nodes[0].convention"
    nodeForm({ c -> c.putAll(["type": null]) })                                                                                             | 412  | "NotEmpty.curveForm.nodes[0].type"
  }

  @WithMockUser
  def "should get calibrated curve list when calibration params are given"() {
    setup:
    def curveGroupId = "randomCurveGroupId"
    def queryParams = getDefaultCurveRequestParams()
    def getCurvesRequest = GetCalibratedCurvesRequest.newOf(parse(queryParams.get("stateDate")),
    Boolean.valueOf(queryParams.get("withArchived")),
    CalculationDiscountingType.valueOf(queryParams.get("discountingType")),
    CalculationStrippingType.valueOf(queryParams.get("calibrationStrippingType")),
    queryParams.get("marketDataGroupId"),
    MarketDataSourceType.valueOf(queryParams.get("marketDataSource")),
    Optional.ofNullable(queryParams.get("curveDate")).map(LocalDate.&parse).orElse(null),
    Optional.ofNullable(queryParams.get("valuationDate")).map(LocalDate.&parse).orElse(null),
    new InstrumentPriceRequirementsForm(curvesPriceType: ASK_PRICE),)
    def requestQueryParams = new LinkedMultiValueMap()
    queryParams.keySet().stream().forEach(param -> requestQueryParams.add(param, queryParams.get(param)))

    when:
    def results = mockMvc
    .perform(get("/curve-group/$curveGroupId/curves", curveGroupId)
    .queryParams(requestQueryParams))
    .andReturn()

    then:
    1 * service.getCurves(curveGroupId, {
      verifyAll(it, BitemporalDate) {
        actualDate == getCurvesRequest.stateDate
      }
    }, getCurvesRequest) >> right([])
    results.response.status == 200
  }

  @WithMockUser
  def "should get curve list when only state date and withArchived params are given"() {
    setup:
    def curveGroupId = "randomCurveGroupId"
    def queryParams = getDefaultCurveRequestParams()
    def requestQueryParams = new LinkedMultiValueMap()
    requestQueryParams.add("stateDate", queryParams.get("stateDate"))
    requestQueryParams.add("withArchived", queryParams.get("withArchived"))
    def stateDate = parse(queryParams.get("stateDate"))
    def withArchived = Boolean.valueOf(queryParams.get("withArchived"))

    when:
    def results = mockMvc
    .perform(get("/curve-group/$curveGroupId/curves")
    .queryParams(requestQueryParams))
    .andReturn()

    then:
    1 * service.getCurves(curveGroupId, {
      verifyAll(it, BitemporalDate) {
        actualDate == stateDate
      }
    }, withArchived) >> right([])
    results.response.status == 200
  }

  @Unroll
  @WithMockUser
  def "when #requestParams are invalid then bad request is returned"() {
    setup:
    def curveGroupId = "randomCurveGroupId"
    def requestQueryParams = new LinkedMultiValueMap()
    requestParams.keySet().stream().forEach(param -> requestQueryParams.add(param, requestParams.get(param)))

    when:
    def results = mockMvc
    .perform(get("/curve-group/$curveGroupId/curves")
    .queryParams(requestQueryParams))

    then:
    0 * service.getCurves(curveGroupId, _ as BitemporalDate, _ as Boolean, _ as Sort)
    results.andExpect(status().is4xxClientError())

    and:
    results.andReturn().getResponse().getContentAsString().indexOf(validationMessage) >= 0

    where:
    requestParams                                                | validationMessage
    getDefaultCurveRequestParams(stateDate: null)                | "NotNull.stateDate"
    getDefaultCurveRequestParams(discountingType: null)          | "NotNull.discountingType"
    getDefaultCurveRequestParams(calibrationStrippingType: null) | "NotNull.calibrationStrippingType"
    getDefaultCurveRequestParams(marketDataGroupId: null)        | "NotEmpty.marketDataGroupId"
    getDefaultCurveRequestParams(marketDataSource: null)         | "NotNull.marketDataSource"
    getDefaultCurveRequestParams(curveDate: null)                | "NotNull.curveDate"
    getDefaultCurveRequestParams(valuationDate: null)            | "NotNull.valuationDate"
  }

  @WithMockUser
  def "should archive curve"() {
    setup:
    1 * service.archiveCurve("curveGroupId", "curveId", parse("2018-05-05"), _) >> right(EntityId
    .entityId("1"))

    when:
    def results = mockMvc.perform(put("/curve-group/curveGroupId/curves/curveId/2018-05-05/archive")
    .content(objectMapper.writeValueAsString([versionForm: [comment: "comment", validFrom: "2000-01-01", stateDate: "2000-01-01", futureVersionsAction: "DELETE"]]))
    .contentType(MediaType.APPLICATION_JSON)
    .with(csrf())).andReturn()

    then:
    results.response.status == 200
  }

  @WithMockUser
  def "should get curves csv"() {
    setup:
    1 * exportService.getCurvesCsvBytes(_, _, _) >> right(FileResponseEntity.csvFile(new ByteArrayResource("test"
    .bytes), "name"))

    when:
    def results = mockMvc.perform(get("/curve-group/curveGroupId/curves/curves-csv")
    .param("stateDate", "2018-05-05")
    .with(csrf()))
    .andReturn()

    then:
    results.response.status == 200
  }

  @Unroll
  @WithMockUser
  def "when getting IR + inflation curve future versions then should decide which valuation date to use: #expectedDateValue"() {
    setup:
    def curveGroupId = "curveGroupId"
    def curveId = "curveId"
    def stateDateValue = queryParams.containsKey("stateDate") ? parse(queryParams.get("stateDate").first()) : null
    def expectedCalibrationOptions = new CalibratedCurvesOptions(parse(expectedDateValue), null, null)

    when:
    def results = mockMvc.perform(get("/curve-group/$curveGroupId/curves/$curveId/$nodeVersion/nodes")
    .queryParams(queryParams)
    .with(csrf()))

    then:
    1 * service.getCurveNodes(curveGroupId, curveId, { it.actualDate == parse(nodeVersion) }, {
      it.stateDate == stateDateValue
    }, expectedCalibrationOptions) >> right([])

    and:
    results.andExpect(status().is2xxSuccessful())

    where:
    expectedDateValue | nodeVersion  | queryParams
    "2022-05-09"      | "2022-05-07" | ["valuationDate": ["2022-05-09"], "stateDate": ["2022-05-08"]] as LinkedMultiValueMap
    "2022-05-08"      | "2022-05-07" | ["stateDate": ["2022-05-08"]] as LinkedMultiValueMap
    "2022-05-07"      | "2022-05-07" | [:] as LinkedMultiValueMap
  }

  @WithMockUser
  def "should upload curves CSV"() {
    setup:
    1 * importService.uploadCurves(_, _, _) >> right([EntityId.entityId("entityId")])

    when:
    def file = new MockMultipartFile("file", "file", MediaType.TEXT_PLAIN_VALUE, "test".bytes)
    def results = mockMvc.perform(multipart("/curve-group/curveGroupId/curves/upload")
    .file(file)
    .param("stateDate", "2018-05-05")
    .param("duplicateAction", ERROR.name())
    .with(csrf()))
    .andReturn()

    then:
    with(results.getResponse()) {
      getStatus() == 200
      getContentAsString().indexOf("entityId") >= 0
    }
  }

  @WithMockUser
  def "should get all IR + Inflation curves nodes CSV"() {
    setup:
    def version = parse("2018-05-05")
    def groupId = "GROUP_ID"

    1 * exportService.getCurvesNodesCsvBytes(groupId, {
      verifyAll(it, BitemporalDate) {
        actualDate == version
      }
    },
    _ as CurveConfigMarketStateForm) >> right(FileResponseEntity.csvFile(new ByteArrayResource("test".bytes), "name"))

    when:
    def results = mockMvc.perform(get("/curve-group/{groupId}/curves/{version}/nodes/csv", groupId, version)
    .with(csrf()))

    then:
    results.andExpect(status().is2xxSuccessful())
  }

  def getDefaultCurveRequestParams(Map args = [:]) {
    def defaultParams = [stateDate                          : "2016-01-01",
      withArchived                       : "true",
      discountingType                    : "DISCOUNT_USD",
      calibrationStrippingType           : "LIBOR",
      marketDataGroupId                  : "groupId",
      marketDataSource                   : "RAW_PRIMARY",
      curveDate                          : "2016-01-02",
      valuationDate                      : "2016-01-03",
      "priceRequirements.curvesPriceType": "ASK_PRICE",]
    def combined = defaultParams + args
    combined
  }

  def curveForm(Closure c = { f -> f }) {
    return ["name"                : "EUR 3M",
      "curveType"           : "IR_INDEX",
      "interpolator"        : "Linear",
      "extrapolatorLeft"    : "Flat",
      "extrapolatorRight"   : "Flat",
      "minGap"              : "1D",
      "clashAction"         : CurveNodeClashAction.DROP_OTHER.getName(),
      "versionForm"         : NewVersionFormV2.newDefault(),
      "yInterpolationMethod": "ZeroRate"].tap(c)
  }

  def nok3MForm(Closure c = { f -> f }) {
    return ["name"                : "NOK 3M",
      "curveType"           : "IR_INDEX",
      "interpolator"        : "Linear",
      "extrapolatorLeft"    : "Flat",
      "extrapolatorRight"   : "Flat",
      "minGap"              : "1D",
      "clashAction"         : CurveNodeClashAction.DROP_OTHER.getName(),
      "versionForm"         : NewVersionFormV2.newDefault(),
      "yInterpolationMethod": "ZeroRate"].tap(c)
  }

  def curveInflationForm(Closure c = { f -> f }) {
    return ["name"                : "GB RPI",
      "curveType"           : "INFLATION_INDEX",
      "interpolator"        : "Linear",
      "extrapolatorLeft"    : "Flat",
      "extrapolatorRight"   : "Flat",
      "minGap"              : "1D",
      "clashAction"         : CurveNodeClashAction.DROP_OTHER.getName(),
      "versionForm"         : NewVersionFormV2.newDefault(),
      "yInterpolationMethod": "PriceIndex"].tap(c)
  }

  def curveFxForm(Closure c = { f -> f }) {
    return ["name"                : "EUR/USD",
      "curveType"           : "XCCY",
      "interpolator"        : "Linear",
      "extrapolatorLeft"    : "Flat",
      "extrapolatorRight"   : "Flat",
      "versionForm"         : NewVersionFormV2.newDefault(),
      "yInterpolationMethod": "ZeroRate"].tap(c)
  }

  def nodeForm(Closure c = { f -> f }) {
    return ["type"      : FIXED_IBOR_SWAP_NODE,
      "convention": EUR_FIXED_1Y_EURIBOR_3M.name,
      "period"    : "1Y"].tap(c)
  }

  def termDepositNodeForm(Closure c = { f -> f }) {
    return ["type"      : TERM_DEPOSIT_NODE,
      "convention": ExtendedTermDepositConventions.NZD_DEPOSIT_T0.name,
      "period"    : null].tap(c)
  }

  def fxNodeForm(Closure c = { f -> f }) {
    return ["type"      : FX_SWAP_NODE,
      "convention": "EUR/USD",
      "period"    : "1Y"].tap(c)
  }

  def fxMarketTenorNodeForm(Closure c = { f -> f }) {
    return ["type"      : FX_SWAP_NODE,
      "convention": "EUR/USD",
      "period"    : "ON"].tap(c)
  }

  def fraNodeForm() {
    return ["type"         : FRA_NODE,
      "convention"   : "AED-EIBOR-3M",
      "fraSettlement": "9M",
      "period"       : "1Y"]
  }

  def immFraNodeForm(Closure c = { f -> f }) {
    return ["type"        : IMM_FRA_NODE,
      "convention"  : ExtendedImmutableImmFraConventions.NOK_NIBOR_3M_IMM.name,
      "serialFuture": "2D+1"].tap(c)
  }
}
