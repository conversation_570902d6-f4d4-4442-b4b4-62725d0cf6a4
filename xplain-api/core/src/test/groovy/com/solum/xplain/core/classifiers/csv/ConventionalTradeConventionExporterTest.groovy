package com.solum.xplain.core.classifiers.csv

import static com.solum.xplain.core.common.csv.CsvColumn.text

import com.opengamma.strata.collect.named.Named
import com.opengamma.strata.product.swap.type.FixedInflationSwapConvention
import com.solum.xplain.core.common.csv.CsvColumn
import com.solum.xplain.core.common.csv.CsvMapper
import spock.lang.Specification

class ConventionalTradeConventionExporterTest extends Specification {
  def "should export curve conventions inflation trade conventions"() {
    setup:
    def bytes = new ConventionalTradeConventionExporter(new TestMapper(), FixedInflationSwapConvention).export()

    expect:
    new String(bytes.getByteArray(), "UTF-8") ==
    """Name
CHF-FIXED-ZC-CH-CPI
CHF-FIXED-ZC-CH-CPI-30360
EUR-FIXED-ZC-EU-AI-CPI
EUR-FIXED-ZC-EU-AI-CPI-30360
EUR-FIXED-ZC-EU-EXT-CPI
EUR-FIXED-ZC-EU-EXT-CPI-30360
EUR-FIXED-ZC-EU-EXT-CPI-CLEARED
EUR-FIXED-ZC-EU-EXT-CPI-CLEARED-30360
EUR-FIXED-ZC-FR-CPI
EUR-FIXED-ZC-FR-CPI-30360
EUR-FIXED-ZC-FR-CPI-CLEARED
EUR-FIXED-ZC-FR-CPI-CLEARED-30360
GBP-FIXED-ZC-GB-CPI
GBP-FIXED-ZC-GB-CPI-30360
GBP-FIXED-ZC-GB-CPI-CLEARED
GBP-FIXED-ZC-GB-CPI-CLEARED-30360
GBP-FIXED-ZC-GB-HICP
GBP-FIXED-ZC-GB-HICP-30360
GBP-FIXED-ZC-GB-RPI
GBP-FIXED-ZC-GB-RPI-30360
GBP-FIXED-ZC-GB-RPI-CLEARED
GBP-FIXED-ZC-GB-RPI-CLEARED-30360
GBP-FIXED-ZC-GB-RPIX
GBP-FIXED-ZC-GB-RPIX-30360
JPY-FIXED-ZC-JP-CPI
JPY-FIXED-ZC-JP-CPI-30360
JPY-FIXED-ZC-JP-CPI_INTERP
JPY-FIXED-ZC-JP-CPI_INTERP-30360
USD-FIXED-ZC-US-CPI
USD-FIXED-ZC-US-CPI-30360
USD-FIXED-ZC-US-CPI-CLEARED
USD-FIXED-ZC-US-CPI-CLEARED-30360
"""
  }

  class TestMapper extends CsvMapper<Named> {
    private static final List<CsvColumn<Named>> COLUMNS =
    [text("name", "Name", Named::getName)]

    protected TestMapper() {
      super(COLUMNS, null)
    }
  }
}
