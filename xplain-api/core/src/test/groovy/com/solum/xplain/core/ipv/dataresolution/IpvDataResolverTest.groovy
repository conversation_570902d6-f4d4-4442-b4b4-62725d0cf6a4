package com.solum.xplain.core.ipv.dataresolution

import static com.solum.xplain.core.ipv.ValuationDataKeyUtils.toValuationDataKey

import com.solum.xplain.core.common.versions.VersionedDataAggregations
import com.solum.xplain.core.company.entity.CompanyLegalEntity
import com.solum.xplain.core.company.repository.CompanyLegalEntityRepository
import com.solum.xplain.core.ipv.nav.entity.CompanyLegalEntityNav
import com.solum.xplain.core.portfolio.event.PortfolioItemsStatesUpdated
import com.solum.xplain.core.portfolio.repository.PortfolioItemRepository
import com.solum.xplain.core.portfolio.repository.PortfolioRepository
import com.solum.xplain.core.portfolio.value.PortfolioView
import java.time.LocalDate
import java.util.stream.Stream
import org.bson.Document
import org.bson.types.ObjectId
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.aggregation.Aggregation
import org.springframework.data.mongodb.core.aggregation.AggregationResults
import spock.lang.Specification

class IpvDataResolverTest extends Specification {

  static final IPV_GROUP_ID = ObjectId.get().toHexString()

  static final COMPANY_ID = "companyId"
  static final ENTITY_ID = "EntityId"
  static final VDK_PREFIX = "COMPANY_ENTITY"
  static final PORTFOLIO_ID = ObjectId.get().toHexString()
  static final PORTFOLIO = new PortfolioView(
  id: PORTFOLIO_ID,
  externalPortfolioId: "PORTFOLIO",
  externalEntityId: "ENTITY",
  externalCompanyId: "COMPANY"
  )

  static final String TRADE_EXT_ID_1 = "ID1"
  static final String TRADE_EXT_ID_2 = "ID2"

  static final LocalDate VALUATION_DATE = LocalDate.of(2021, 01, 01)

  IpvDataResolutionRepository ipvDataResolutionRepository = Mock()
  PortfolioRepository portfolioRepository = Mock()
  PortfolioItemRepository portfolioItemRepository = Mock()
  CompanyLegalEntityRepository companyLegalEntityRepository = Mock()
  MongoOperations mongoOperations = Mock()

  IpvDataResolver resolver = new IpvDataResolver(
  ipvDataResolutionRepository,
  portfolioItemRepository,
  companyLegalEntityRepository,
  mongoOperations
  )

  def "should resolve ipv data"() {
    setup:
    def resolvedVdk = vdk(PORTFOLIO, TRADE_EXT_ID_1)
    def unresolvedVdk = vdk(PORTFOLIO, TRADE_EXT_ID_2)
    def groupDataKeys = Set.of(resolvedVdk, unresolvedVdk)

    when:
    resolver.resolveIpvData()

    then:
    1 * ipvDataResolutionRepository.getNonResolvedKeys() >> Stream.of(
      ipvDataGroupKey(IPV_GROUP_ID, VALUATION_DATE, resolvedVdk),
      ipvDataGroupKey(IPV_GROUP_ID, VALUATION_DATE, unresolvedVdk)
      )

    1 * portfolioItemRepository.resolvedValuationDataKeys({ it.getActualDate() == VALUATION_DATE }, groupDataKeys) >> [resolvedVdk]
    0 * portfolioItemRepository.allValuationDataKeys([PORTFOLIO_ID], { it.getActualDate() == VALUATION_DATE }) >> Set.of(vdk(PORTFOLIO, TRADE_EXT_ID_1))

    1 * ipvDataResolutionRepository.updateValuesResolved(
      IPV_GROUP_ID,
      Set.of(vdk(PORTFOLIO, TRADE_EXT_ID_1)),
      VALUATION_DATE,
      true
      ) >> 1
    1 * ipvDataResolutionRepository.updateValuesAsUnresolved(IPV_GROUP_ID, VALUATION_DATE) >> 1
  }

  def "should resolve ipv nav data"() {
    AggregationResults<Document> mockResults = Mock()

    when:
    resolver.resolveCompanyLegalEntityNavData()

    then:
    1 * mongoOperations.aggregate({ Aggregation aggregation ->
      true
    }, CompanyLegalEntityNav.class, Document.class) >> mockResults
  }

  def "should resolve ipv nav data with specific group id"() {
    AggregationResults<Document> mockResults = Mock()

    when:
    resolver.resolveCompanyLegalEntityNavData(IPV_GROUP_ID)

    then:
    1 * mongoOperations.aggregate({ Aggregation aggregation ->
      def pipelineStr = aggregation.toString()
      pipelineStr.contains("groupId") && pipelineStr.contains(IPV_GROUP_ID)
    }, CompanyLegalEntityNav.class, Document.class) >> mockResults
  }

  def "should update portfolio IPV data as non-resolved"() {
    when:
    resolver.updateArchivedPortfolioIpvDataNonResolved([PORTFOLIO_ID])

    then:
    1 * portfolioItemRepository.allValuationDataKeys([PORTFOLIO_ID], { it.getActualDate() == LocalDate.now() }) >> Set.of(vdk(PORTFOLIO, TRADE_EXT_ID_1))
    1 * ipvDataResolutionRepository.updateAllValuesUnresolved(Set.of(vdk(PORTFOLIO, TRADE_EXT_ID_1))) >> 1
  }

  def "should update company legal entity IPV data as non-resolved"() {
    when:
    resolver.updateCompanyLegalEntityNavDataNonResolved(COMPANY_ID, ENTITY_ID)

    then:
    1 * companyLegalEntityRepository.archivedLegalEntities(COMPANY_ID, ENTITY_ID) >> List.of(new CompanyLegalEntity(vdkPrefix: VDK_PREFIX))
    1 * ipvDataResolutionRepository.updateCompanyLegalEntityNavValuesAsNonResolved([VDK_PREFIX]) >> 1
  }

  def "should update portfolio items IPV data as non-resolved"() {
    when:
    def portfolioItemChange = new PortfolioItemsStatesUpdated.VDKUpdate(
      vdk(PORTFOLIO, TRADE_EXT_ID_1),
      LocalDate.ofEpochDay(0),
      VersionedDataAggregations.MAX_DATE
      )

    resolver.updatePortfolioItemsIpvDataNonResolved(Set.of(portfolioItemChange))

    then:
    0 * portfolioRepository.portfolio(PORTFOLIO_ID) >> Optional.of(PORTFOLIO)
    0 * portfolioItemRepository._
    1 * ipvDataResolutionRepository.updateAllValuesNonResolved(
      Set.of(vdk(PORTFOLIO, TRADE_EXT_ID_1)),
      LocalDate.ofEpochDay(0),
      VersionedDataAggregations.MAX_DATE,
      ) >> 1
  }

  def "should update group IPV data as non-resolved"() {
    when:
    resolver.updateIpvGroupDataNonResolved(IPV_GROUP_ID)

    then:
    1 * ipvDataResolutionRepository.updateValuesNonResolved(IPV_GROUP_ID) >> 1
  }

  def static ipvDataGroupKey(String groupId, LocalDate date, String key) {
    new IpvDataGroupDateKey(groupId, date, key)
  }

  def static vdk(PortfolioView portfolio, String externalTradeId) {
    toValuationDataKey(portfolio.externalCompanyId,
      portfolio.externalEntityId,
      portfolio.externalPortfolioId,
      externalTradeId
      )
  }
}
