package com.solum.xplain.core.curvegroup.curve.entity

import static com.opengamma.strata.basics.currency.Currency.EUR
import static com.opengamma.strata.basics.currency.Currency.USD
import static com.opengamma.strata.basics.index.IborIndices.EUR_EURIBOR_3M
import static com.opengamma.strata.basics.index.IborIndices.EUR_EURIBOR_6M
import static com.opengamma.strata.basics.index.IborIndices.USD_LIBOR_3M
import static com.opengamma.strata.basics.index.OvernightIndices.EUR_EONIA
import static com.opengamma.strata.basics.index.OvernightIndices.GBP_SONIA
import static com.opengamma.strata.basics.index.PriceIndices.GB_RPI
import static com.opengamma.strata.market.curve.CurveNodeClashAction.DROP_OTHER
import static com.opengamma.strata.market.curve.CurveNodeClashAction.EXCEPTION
import static com.opengamma.strata.product.swap.type.FixedIborSwapConventions.EUR_FIXED_1Y_EURIBOR_3M
import static com.opengamma.strata.product.swap.type.FixedOvernightSwapConventions.EUR_FIXED_1Y_EONIA_OIS
import static com.opengamma.strata.product.swap.type.XCcyIborIborSwapConventions.EUR_EURIBOR_3M_USD_LIBOR_3M
import static com.solum.xplain.core.classifiers.CurveNodeTypes.FIXED_OVERNIGHT_SWAP_NODE
import static com.solum.xplain.core.classifiers.CurveNodeTypes.XCCY_IBOR_IBOR_SWAP_NODE
import static com.solum.xplain.core.curvegroup.curve.entity.CurveNodeBuilder.curveNode
import static com.solum.xplain.core.curvegroup.curve.entity.CurveNodeBuilder.curveNode2Y
import static com.solum.xplain.core.curvegroup.curve.entity.CurveNodeBuilder.fraCurveNode
import static com.solum.xplain.core.curvegroup.curve.entity.CurveNodeBuilder.iborFutureCurveNode
import static com.solum.xplain.core.curvegroup.curve.entity.CurveSample.eur3m
import static com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType.FIXED_IBOR_SWAP
import static com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition.ofFxCurve
import static com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition.ofIrCurve
import static java.time.LocalDate.now
import static java.util.Optional.empty
import static java.util.Optional.of

import com.opengamma.strata.basics.ReferenceData
import com.opengamma.strata.basics.currency.Currency
import com.opengamma.strata.basics.date.DayCounts
import com.opengamma.strata.data.MarketData
import com.opengamma.strata.market.ValueType
import com.opengamma.strata.market.curve.CurveNodeDateOrder
import com.opengamma.strata.market.curve.InterpolatedNodalCurveDefinition
import com.opengamma.strata.market.curve.interpolator.CurveExtrapolators
import com.opengamma.strata.market.curve.interpolator.CurveInterpolators
import com.opengamma.strata.product.fra.type.FraConvention
import com.opengamma.strata.product.fx.type.FxSwapConvention
import com.solum.xplain.core.classifiers.CurveNodeTypes
import com.solum.xplain.core.curvegroup.curve.classifier.CurveType
import com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType
import com.solum.xplain.core.curvemarket.node.ValidNodesFilter
import com.solum.xplain.core.error.Error
import com.solum.xplain.core.error.ErrorItem
import java.time.LocalDate
import java.util.stream.Collectors
import spock.lang.Specification
import spock.lang.Unroll

class CurveTest extends Specification {
  def REF_DATA = ReferenceData.standard()
  def MARKET_DATA = Mock(MarketData)
  static WARNINGS = { it -> null }

  def "should return error if nodes is less than 2"() {
    setup:
    def version = new CurveBuilder().nodes([curveNode()]).build()

    when:
    def result = version.curveDefinition({ 0d }, now(), ValidNodesFilter.EMPTY_FILTER, MARKET_DATA, REF_DATA, WARNINGS, false, false)

    then:
    result.isLeft()
    result.left().getOrNull().reason == Error.CALIBRATION_ERROR
    result.left().getOrNull().description == "Curve AUD 6M must have at least 2 nodes"
  }

  def "should return error if no nodes"() {
    setup:
    def version = new CurveBuilder().nodes([]).build()

    when:
    def result = version.curveDefinition({ 0d }, now(), ValidNodesFilter.EMPTY_FILTER, MARKET_DATA, REF_DATA, WARNINGS, false, false)

    then:
    result.isLeft()
    result.left().getOrNull().reason == Error.CALIBRATION_ERROR
    result.left().getOrNull().description == "Curve AUD 6M must have at least 2 nodes"
  }

  def "should return if curve is offshore"() {
    setup:
    def version = new CurveBuilder().name(name).build()

    expect:
    version.isOffshoreCurve() == isOffshore

    where:
    name              | isOffshore
    "CNY 1W"          | false
    "CNY 1W Offshore" | true
  }

  def "should return correct curve definition"() {
    setup:
    def version = new CurveBuilder().nodes([
      curveNode2Y(CurveNodeTypes.FIXED_IBOR_SWAP_NODE, EUR_FIXED_1Y_EURIBOR_3M.name),
      curveNode(CurveNodeTypes.FIXED_IBOR_SWAP_NODE, EUR_FIXED_1Y_EURIBOR_3M.name)
    ]).build()

    when:
    def result = version.curveDefinition({ 0d }, now(), ValidNodesFilter.EMPTY_FILTER, MARKET_DATA, REF_DATA, WARNINGS, false, false)

    then:
    result.isRight()
    def curveDef = (InterpolatedNodalCurveDefinition) result.right().get()
    curveDef.nodes.size() == 2
    curveDef.nodes[0].label == "1Y"
    curveDef.nodes[1].label == "2Y"
    curveDef.getName().getName() == "AUD 6M"
    curveDef.getDayCount().get() == DayCounts.ACT_365F
    curveDef.getXValueType() == ValueType.YEAR_FRACTION
    curveDef.getYValueType() == ValueType.ZERO_RATE
    curveDef.getInterpolator() == CurveInterpolators.LINEAR
    curveDef.getExtrapolatorLeft() == CurveExtrapolators.FLAT
    curveDef.getExtrapolatorRight() == CurveExtrapolators.FLAT
  }

  def "should return correct curve definition with clashing futures when other node #otherNode and #clashAction #minGap then #remainingNodes #warning"() {
    setup:
    def errors = new ArrayList<ErrorItem>()
    def version = new CurveBuilder()
      .clashAction(clashAction)
      .minGap(minGap)
      .nodes([
        curveNode(CurveNodeTypes.FIXED_IBOR_SWAP_NODE, EUR_FIXED_1Y_EURIBOR_3M.name, "1D"),
        curveNode(CurveNodeTypes.FIXED_IBOR_SWAP_NODE, EUR_FIXED_1Y_EURIBOR_3M.name, otherNode),
        iborFutureCurveNode("EUR-EURIBOR-3M-Quarterly-IMM", "2D+1"),
      ]).build()

    when:
    def result = version.curveDefinition({ 0d }, LocalDate.of(2023, 1, 1), ValidNodesFilter.EMPTY_FILTER, MARKET_DATA, REF_DATA, { it ->
      errors.addAll(it)
    }, false, false)

    then:
    result.isRight()
    def curveDef = (InterpolatedNodalCurveDefinition) result.right().get()
    curveDef.nodes.size() == remainingNodes.size() + 1
    curveDef.nodes[0].label == "1D"
    curveDef.nodes[1].label == remainingNodes[0]
    remainingNodes.size() == 1 || curveDef.nodes[2].label == remainingNodes[1]
    warning == null || errors[0].description == warning

    where:
    otherNode | clashAction  | minGap | remainingNodes   | warning
    "100D"    | "DROP_OTHER" | "1D"   | ["100D", "2D+1"] | null
    "100D"    | "DROP_THIS"  | "1D"   | ["100D", "2D+1"] | null
    "163D"    | "DROP_OTHER" | "1D"   | ["2D+1"]         | "Curve AUD 6M clashing node 163D was dropped"
    "163D"    | "DROP_THIS"  | "1D"   | ["163D"]         | "Curve AUD 6M clashing node 2D+1 was dropped"
    "164D"    | "DROP_OTHER" | "1D"   | ["2D+1", "164D"] | null
    "164D"    | "DROP_THIS"  | "1D"   | ["2D+1", "164D"] | null
    "164D"    | "DROP_OTHER" | "2D"   | ["2D+1"]         | "Curve AUD 6M clashing node 164D was dropped"
    "164D"    | "DROP_THIS"  | "2D"   | ["164D"]         | "Curve AUD 6M clashing node 2D+1 was dropped"
  }

  @Unroll
  def "should drop #node1 node with warning if #node2 node date matches on #date"() {
    // Do we use the empty filter and specify clashAction, or implement a new filter?
    setup:
    def minGap = "7D"
    def errors = new ArrayList<ErrorItem>()
    def version = new CurveBuilder()
      .name("XAU/EUR")
      .curveType(CurveType.XCCY)
      .clashAction(EXCEPTION.name)
      .minGap(minGap)
      .nodes([usdCnhXccyNode(node1), usdCnhXccyNode(node2), usdCnhXccyNode("1Y"),]).build()

    when:
    def result = version.curveDefinition({ 0d }, LocalDate.parse(date), ValidNodesFilter.EMPTY_FILTER, MARKET_DATA, REF_DATA, { it ->
      errors.addAll(it)
    }, false, false)

    then:
    errors.size() == 1
    errors[0].description == "Market tenor node with key ${node1}_USD/CNH was dropped since it clashes with the next node"
    result.isRight()
    def curveDef = (InterpolatedNodalCurveDefinition) result.right().get()
    curveDef.nodes.size() == 2
    curveDef.nodes[0].label == node2

    where:
    date         | node1 | node2
    "2023-09-24" | "1W" | "2W"
    "2023-09-17" | "2W" | "3W"
    "2023-09-10" | "3W" | "4W"
    "2023-09-03" | "4W" | "1M"
  }

  def usdCnhXccyNode(String period) {
    def node = new CurveNode()
    node.setType(CurveNodeTypes.FX_SWAP_NODE)
    node.setConvention(FxSwapConvention.of("USD/CNH").toString())
    node.setPeriod(period)
    return node
  }

  def "should return correct curve definition with forced ISDA interpolators"() {
    setup:
    def version = new CurveBuilder().nodes([
      curveNode2Y(CurveNodeTypes.FIXED_IBOR_SWAP_NODE, EUR_FIXED_1Y_EURIBOR_3M.name),
      curveNode(CurveNodeTypes.FIXED_IBOR_SWAP_NODE, EUR_FIXED_1Y_EURIBOR_3M.name)
    ]).build()

    when:
    def result = version.curveDefinition({ 0d }, now(), ValidNodesFilter.EMPTY_FILTER, MARKET_DATA, REF_DATA, WARNINGS, true, false)

    then:
    result.isRight()
    def curveDef = (InterpolatedNodalCurveDefinition) result.right().get()
    curveDef.getInterpolator() == CurveInterpolators.PRODUCT_LINEAR
    curveDef.getExtrapolatorLeft() == CurveExtrapolators.FLAT
    curveDef.getExtrapolatorRight() == CurveExtrapolators.PRODUCT_LINEAR
  }

  def "should return correct definition with curve shifts"() {
    setup:
    def additionalSpread = 5d
    def version = new CurveBuilder().nodes([
      curveNode2Y(CurveNodeTypes.FIXED_IBOR_SWAP_NODE, EUR_FIXED_1Y_EURIBOR_3M.name),
      curveNode(CurveNodeTypes.FIXED_IBOR_SWAP_NODE, EUR_FIXED_1Y_EURIBOR_3M.name)
    ]).build()

    when:
    def result = version.curveDefinition(
      { additionalSpread },
      now(),
      ValidNodesFilter.EMPTY_FILTER,
      MARKET_DATA, REF_DATA,
      WARNINGS,
      true,
      false)

    then:
    result.isRight()
    result.getOrNull().nodes[0].additionalSpread == additionalSpread
  }

  @Unroll
  def "should order nodes with order #instruments"() {
    setup:
    def version = new CurveBuilder()
      .name("EUR 3M")
      .nodes([
        curveNode(CurveNodeTypes.FIXED_IBOR_SWAP_NODE, EUR_FIXED_1Y_EURIBOR_3M.name, "2Y"),
        iborFutureCurveNode("EUR-EURIBOR-3M-Quarterly-IMM", "2D+1"),
        iborFutureCurveNode("EUR-EURIBOR-3M-Quarterly-IMM", "2D+7"),
        iborFutureCurveNode("EUR-EURIBOR-3M-Quarterly-IMM", "2D+11"),
        curveNode(CurveNodeTypes.FIXED_IBOR_SWAP_NODE, EUR_FIXED_1Y_EURIBOR_3M.name, "4M"),
        fraCurveNode(FraConvention.of(EUR_EURIBOR_6M).name, "6M", "9M"),
        fraCurveNode(FraConvention.of(EUR_EURIBOR_6M).name, "6M", "3M"),
        fraCurveNode(FraConvention.of(EUR_EURIBOR_6M).name, "6M", "12M"),
        fraCurveNode(FraConvention.of(EUR_EURIBOR_6M).name, "6M", "6M"),
        curveNode(CurveNodeTypes.FIXED_IBOR_SWAP_NODE, EUR_FIXED_1Y_EURIBOR_3M.name, "3Y")
      ])
      .build()

    when:
    def nodes = version.orderedNodes(sortDate, REF_DATA)

    then:
    nodes.instrument == instruments

    where:
    sortDate                      | instruments
    LocalDate.parse("2020-12-16") | ["4M", "2D+1", "3Mx9M", "6Mx12M", "9Mx15M", "12Mx18M", "2Y", "2D+7", "3Y", "2D+11"]
    LocalDate.parse("2020-02-10") | ["4M", "2D+1", "3Mx9M", "6Mx12M", "9Mx15M", "12Mx18M", "2D+7", "2Y", "2D+11", "3Y"]
  }


  @Unroll
  def "should resolve curve #name to projection currencies #currency"() {
    setup:
    def cv = new CurveBuilder().name(name).build()

    expect:
    def projectionCurrency = cv.projectionCurrency()
    projectionCurrency == currency

    where:
    name                      | currency
    "EUR 3M"                  | of(Currency.of("EUR"))
    "GBP SONIA"               | of(Currency.of("GBP"))
    "CHS vs USD Invalid Name" | empty()
    "CHS 3M vs USD 3M"        | empty()
  }

  @Unroll
  def "should resolve index for curve name #name to #result"() {
    setup:
    def cv = new CurveBuilder().name(name).curveType(curveType).build()

    expect:
    result == cv.index()

    where:
    name        | curveType                 | result
    "EUR 3M"    | CurveType.IR_INDEX        | of(EUR_EURIBOR_3M)
    "GB RPI"    | CurveType.INFLATION_INDEX | of(GB_RPI)
    "GBP SONIA" | CurveType.IR_INDEX        | of(GBP_SONIA)
  }

  @Unroll
  def "should resolve #nodeType #convention to required #result"() {
    setup:
    def cv = new CurveBuilder()
      .nodes([
        new CurveNodeBuilder()
        .type(nodeType)
        .convention(convention)
        .build()
      ])

      .build()

    expect:
    result == cv.requiredIndices()

    where:
    nodeType                  | convention                       | result
    FIXED_OVERNIGHT_SWAP_NODE | EUR_FIXED_1Y_EONIA_OIS.name      | [EUR_EONIA]
    XCCY_IBOR_IBOR_SWAP_NODE  | EUR_EURIBOR_3M_USD_LIBOR_3M.name | [EUR_EURIBOR_3M, USD_LIBOR_3M]
  }

  @Unroll
  def "should return #result if it is ois curve with name #name"() {
    setup:
    def cv = new CurveBuilder()
      .name(name)
      .build()

    expect:
    result == cv.isOIS(EUR)

    where:
    name        | result
    "EUR 3M"    | false
    "USD FUND"  | false
    "EUR EONIA" | true
  }


  @Unroll
  def "should parse date order #result for #minGap and #clashAction"() {
    setup:
    def version = new CurveBuilder()
      .clashAction(clashAction)
      .minGap(minGap)
      .build()

    expect:
    version.parseDateOrder(now()) == result

    where:
    minGap | clashAction       | result
    "2D"   | DROP_OTHER.name() | CurveNodeDateOrder.of(2, DROP_OTHER)
    ""     | ""                | CurveNodeDateOrder.of(7, DROP_OTHER)
  }

  def "should return correct instrument"() {
    setup:
    def curve = eur3m()

    when:
    def result = curve.allInstruments()

    then:
    result.size() == 2

    result[0] == ofIrCurve("EUR", "EUR 3M", FIXED_IBOR_SWAP,
      "1D", "1D", "1D_EUR-FIXED-1Y-EURIBOR-3M", "EUR 12M 30U/360 VS 3M SWAP 1D")
    result[1] == ofIrCurve("EUR", "EUR 3M", FIXED_IBOR_SWAP,
      "1Y", "1Y", "1Y_EUR-FIXED-1Y-EURIBOR-3M", "EUR 12M 30U/360 VS 3M SWAP 1Y")
  }

  def "should return correct instrument when index is null"() {
    setup:
    def node = Mock(CurveNode)
    def curve = new CurveBuilder().name("EUR/USD").curveType(CurveType.XCCY).nodes([node]).build()
    def definition = ofFxCurve("EUR/USD", "EUR/USD", CoreInstrumentType.FX_SWAP, "1Y", "1Y", "k", "name")
    1 * node.instrumentFx(curve.getName(), curve.getName()) >> definition

    when:
    def result = curve.allInstruments()

    then:
    result.size() == 1
    result[0] == definition
  }

  def "should return correct xccy currencies"() {
    setup:
    def curve = new CurveBuilder().name("EUR/USD").curveType(CurveType.XCCY).build()

    when:
    def result = curve.xccyCurrencies()

    then:
    result.collect(Collectors.toSet()).containsAll([EUR, USD])
  }
}
