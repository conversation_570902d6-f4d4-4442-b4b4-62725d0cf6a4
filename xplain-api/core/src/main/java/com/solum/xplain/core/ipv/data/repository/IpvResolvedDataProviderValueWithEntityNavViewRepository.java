package com.solum.xplain.core.ipv.data.repository;

import com.solum.xplain.core.ipv.data.entity.IpvResolvedDataProviderValueWithEntityNavView;
import com.solum.xplain.core.ipv.data.repository.fragment.IpvResolvedDataProviderValueViewQueries;

/**
 * Repository for IPV resolved data provider values with Entity NAV. This view returns resolved and
 * unarchived results only, and includes NAV with other provider data rather than returning it
 * separately.
 */
public interface IpvResolvedDataProviderValueWithEntityNavViewRepository
    extends IpvResolvedDataProviderValueViewBaseRepository<
            IpvResolvedDataProviderValueWithEntityNavView>,
        IpvResolvedDataProviderValueViewQueries {}
