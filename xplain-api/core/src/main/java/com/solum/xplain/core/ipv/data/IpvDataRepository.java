package com.solum.xplain.core.ipv.data;

import static com.solum.xplain.core.common.AggregationUtils.getDate;
import static com.solum.xplain.core.common.versions.State.ACTIVE;
import static com.solum.xplain.core.company.CompanySettingsType.BESPOKE;
import static com.solum.xplain.core.company.entity.CompanyLegalEntityValuationSettings.COMPANY_ENTITY_VALUATION_SETTINGS_COLLECTION;
import static com.solum.xplain.core.company.entity.CompanyValuationSettings.COMPANY_VALUATION_SETTINGS_COLLECTION;
import static com.solum.xplain.core.error.Error.OBJECT_NOT_FOUND;
import static com.solum.xplain.core.ipv.data.IpvDataType.EXCLUDE_NAV;
import static com.solum.xplain.core.ipv.data.IpvDataType.NAV;
import static com.solum.xplain.core.providers.DataProvider.NAV_PROVIDER_CODE;
import static com.solum.xplain.core.utils.PathUtils.joinPaths;
import static com.solum.xplain.core.utils.mongo.MongoVariables.POSITIONAL_OPERATOR;
import static java.lang.String.format;
import static org.springframework.data.domain.Sort.Direction.DESC;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.addFields;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.limit;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.lookup;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.match;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.newAggregation;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.project;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.sort;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.unwind;
import static org.springframework.data.mongodb.core.aggregation.Fields.UNDERSCORE_ID;
import static org.springframework.data.mongodb.core.query.Criteria.where;
import static org.springframework.data.mongodb.core.query.Query.query;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.ScrollRequest;
import com.solum.xplain.core.common.ScrollableEntry;
import com.solum.xplain.core.common.ScrollableEntrySupport;
import com.solum.xplain.core.common.value.ArchiveForm;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.common.versions.VersionedEntity;
import com.solum.xplain.core.datavalue.DataValueMongoOperations;
import com.solum.xplain.core.datavalue.DataValueUpdateResolver;
import com.solum.xplain.core.datavalue.DataValuesHolder;
import com.solum.xplain.core.datavalue.VersionedValue;
import com.solum.xplain.core.datavalue.csv.DataUpdateSummary;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.ipv.AbstractIpvRepository;
import com.solum.xplain.core.ipv.data.entity.BaseIpvDataValue;
import com.solum.xplain.core.ipv.data.entity.IpvDataValue;
import com.solum.xplain.core.ipv.data.entity.IpvDataValueVersion;
import com.solum.xplain.core.ipv.data.form.IpvDataProviderValueFilter;
import com.solum.xplain.core.ipv.data.form.IpvDataProviderValueForm;
import com.solum.xplain.core.ipv.data.form.IpvDataProviderValueUpdateForm;
import com.solum.xplain.core.ipv.data.repository.IpvResolvedDataProviderValueWithEntityNavViewRepository;
import com.solum.xplain.core.ipv.data.repository.IpvResolvedDataProviderValueWithTradeNavViewRepository;
import com.solum.xplain.core.ipv.data.value.ExceptionManagementSettingsProvider;
import com.solum.xplain.core.ipv.data.value.IpvDataNavValueView;
import com.solum.xplain.core.ipv.data.value.IpvDataProviderValueView;
import com.solum.xplain.core.ipv.nav.entity.CompanyLegalEntityNav;
import com.solum.xplain.core.settings.entity.GlobalValuationSettings;
import com.solum.xplain.core.users.AuditUser;
import com.solum.xplain.shared.utils.filter.TableFilter;
import io.atlassian.fugue.Either;
import io.atlassian.fugue.Eithers;
import jakarta.inject.Provider;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Stream;
import org.bson.Document;
import org.springframework.core.convert.ConversionService;
import org.springframework.data.domain.AuditorAware;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.AggregationOptions;
import org.springframework.data.mongodb.core.aggregation.ArrayOperators;
import org.springframework.data.mongodb.core.aggregation.ComparisonOperators;
import org.springframework.data.mongodb.core.aggregation.ConditionalOperators;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;

@Repository
public class IpvDataRepository extends AbstractIpvRepository<IpvDataValueVersion, IpvDataValue> {

  private static final String ENTITY_SETTINGS = "entitySettings";
  private static final String COMPANY_SETTINGS = "companySettings";
  private static final String VALUES = "values";
  private static final String MAX_RECORD_DATE = "maxRecordDate";
  private static final String APPLICABLE_REPORTING_CURRENCY = "applicableReportingCurrency";
  private static final String REPORTING_CURRENCY_MISMATCH = "reportingCurrencyMismatch";

  private static final String GLOBAL_REPORTING_CURRENCY_MISSING =
      "Global reporting currency is not configured";

  private final MongoOperations mongoOperations;
  private final IpvDataMapper ipvDataMapper;
  private final ConversionService conversionService;
  private final IpvDataModificationHandler ipvDataModificationHandler;
  private final ScrollableEntrySupport scrollableEntrySupport;
  private final DataValueMongoOperations<IpvDataValueVersion, IpvDataValue> dataOperations;
  private final IpvResolvedDataProviderValueWithTradeNavViewRepository
      resolvedTradeNavViewRepository;
  private final IpvResolvedDataProviderValueWithEntityNavViewRepository
      resolvedEntityNavViewRepository;
  private final Provider<ExceptionManagementSettingsProvider> exceptionManagementSettingsProvider;

  public IpvDataRepository(
      MongoOperations mongoOperations,
      IpvDataMapper ipvDataMapper,
      ConversionService conversionService,
      IpvDataModificationHandler ipvDataModificationHandler,
      ScrollableEntrySupport scrollableEntrySupport,
      AuditorAware<AuditUser> auditorAware,
      IpvResolvedDataProviderValueWithTradeNavViewRepository resolvedTradeNavViewRepository,
      Provider<ExceptionManagementSettingsProvider> exceptionManagementSettingsProvider,
      IpvResolvedDataProviderValueWithEntityNavViewRepository resolvedEntityNavViewRepository) {
    this.mongoOperations = mongoOperations;
    this.ipvDataMapper = ipvDataMapper;
    this.conversionService = conversionService;
    this.ipvDataModificationHandler = ipvDataModificationHandler;
    this.scrollableEntrySupport = scrollableEntrySupport;
    this.resolvedTradeNavViewRepository = resolvedTradeNavViewRepository;
    this.dataOperations =
        new DataValueMongoOperations<>(IpvDataValue.class, auditorAware, mongoOperations);
    this.exceptionManagementSettingsProvider = exceptionManagementSettingsProvider;
    this.resolvedEntityNavViewRepository = resolvedEntityNavViewRepository;
  }

  public EntityId createValue(@NonNull String groupId, @NonNull IpvDataProviderValueForm form) {
    var result = dataOperations.insert(ipvDataMapper.newProviderValue(groupId, form));
    ipvDataModificationHandler.onIpvDataValueCreation(groupId);
    return result;
  }

  public Stream<IpvDataValue> valuesStream(
      String groupId, BitemporalDate date, IpvDataType dataType) {

    var criteria = activeValuesCriteria(groupId, date);

    if (dataType == NAV) {
      criteria = criteria.and(IpvDataValue.Fields.provider).is("NAV");
    } else if (dataType == EXCLUDE_NAV) {
      criteria = criteria.and(IpvDataValue.Fields.provider).ne("NAV");
    }
    return mongoOperations.stream(query(criteria), IpvDataValue.class);
  }

  public boolean valueExists(
      @NonNull String groupId,
      @NonNull BitemporalDate date,
      @NonNull String key,
      @NonNull String provider) {
    var criteria =
        where(BaseIpvDataValue.Fields.groupId)
            .is(groupId)
            .and(DataValuesHolder.Fields.date)
            .is(date.getActualDate())
            .andOperator(validAtStateDateCriteria(date, false))
            .and(BaseIpvDataValue.Fields.key)
            .is(key)
            .and(IpvDataValue.Fields.provider)
            .is(provider);
    return mongoOperations.exists(query(criteria), IpvDataValue.class);
  }

  public ScrollableEntry<IpvDataProviderValueView> getValueViews(
      @NonNull String groupId,
      @NonNull IpvDataProviderValueFilter filter,
      @NonNull TableFilter tableFilter,
      @NonNull ScrollRequest scrollRequest) {

    String navLevel = getGlobalNavLevelSettings(BitemporalDate.newOf(filter.getValuationDate()));

    // Append relevant NAV information for the resolved IPV data only
    if (includeResolvedNavData(filter)) {
      var repository =
          navLevel.equals("ENTITY_LEVEL")
              ? resolvedEntityNavViewRepository
              : resolvedTradeNavViewRepository;
      return repository.getValueViews(
          groupId, filter.getValuationDate(), tableFilter, scrollRequest);
    }
    var operations =
        viewAggregations(filter.criteria(groupId, EXCLUDE_NAV))
            .add(match(tableFilter.criteria(IpvDataProviderValueView.class, conversionService)))
            .build();
    return scrollableEntrySupport.getScrollableEntry(
        operations, scrollRequest, IpvDataValue.class, IpvDataProviderValueView.class);
  }

  public ScrollableEntry<IpvDataNavValueView> getNavValueViews(
      @NonNull String groupId,
      @NonNull IpvDataProviderValueFilter filter,
      @NonNull TableFilter tableFilter,
      @NonNull ScrollRequest scrollRequest) {
    String navLevel = getGlobalNavLevelSettings(BitemporalDate.newOf(filter.getValuationDate()));
    boolean isEntityLevel = navLevel.equals("ENTITY_LEVEL");

    var filterCriteria = isEntityLevel ? filter.criteria(groupId) : filter.criteria(groupId, NAV);

    var operations =
        viewAggregations(filterCriteria)
            .add(match(tableFilter.criteria(IpvDataNavValueView.class, conversionService)))
            .build();

    var navData =
        isEntityLevel
            ? scrollableEntrySupport.getScrollableEntry(
                operations, scrollRequest, CompanyLegalEntityNav.class, IpvDataNavValueView.class)
            : scrollableEntrySupport.getScrollableEntry(
                operations, scrollRequest, IpvDataValue.class, IpvDataNavValueView.class);

    if (isEntityLevel) {
      validateReportingCurrency(navData, filter.getValuationDate());
    }

    return navData;
  }

  /**
   * Validates reporting currency consistency between NAV currency and its entities/company/global
   * settings If a mismatch is found, sets the reportingCurrencyMismatch to true.
   */
  void validateReportingCurrency(
      ScrollableEntry<IpvDataNavValueView> navData, LocalDate valuationDate) {
    if (navData == null || navData.getContent() == null || navData.getContent().isEmpty()) {
      return;
    }

    // Build relevant nav map
    Map<String, IpvDataNavValueView> relevantNavMap = new HashMap<>();
    navData
        .getContent()
        .forEach(
            nav -> {
              if (Boolean.TRUE.equals(nav.getResolved())
                  && nav.getCurrency() != null
                  && !nav.getCurrency().trim().isEmpty()) {
                relevantNavMap.put(nav.getId(), nav);
              } else {
                nav.setReportingCurrencyMismatch(false);
              }
            });

    if (relevantNavMap.isEmpty()) {
      return;
    }

    // Get global reporting currency
    String globalReportingCurrency =
        Optional.ofNullable(
                mongoOperations.findOne(
                    query(where(VersionedEntity.Fields.state).is(ACTIVE)),
                    GlobalValuationSettings.class))
            .map(GlobalValuationSettings::getReportingCurrency)
            .orElseThrow(() -> new IllegalArgumentException(GLOBAL_REPORTING_CURRENCY_MISSING));

    var aggregation =
        mongoOperations.aggregate(
            newAggregation(
                match(Criteria.where(UNDERSCORE_ID).in(relevantNavMap.keySet())),

                // Entity settings lookup
                lookup()
                    .from(COMPANY_ENTITY_VALUATION_SETTINGS_COLLECTION)
                    .localField(CompanyLegalEntityNav.Fields.legalEntityId)
                    .foreignField(VersionedEntity.Fields.entityId)
                    .pipeline(
                        match(
                            where(VersionedEntity.Fields.state)
                                .is(ACTIVE)
                                .and(VersionedEntity.Fields.validFrom)
                                .lt(valuationDate)),
                        sort(
                            Sort.by(DESC, VersionedEntity.Fields.validFrom)
                                .and(Sort.by(DESC, VersionedEntity.Fields.modifiedAt))),
                        limit(1))
                    .as(ENTITY_SETTINGS),
                unwind(ENTITY_SETTINGS, true),

                // Company settings lookup
                lookup()
                    .from(COMPANY_VALUATION_SETTINGS_COLLECTION)
                    .localField(CompanyLegalEntityNav.Fields.companyId)
                    .foreignField(VersionedEntity.Fields.entityId)
                    .pipeline(
                        match(
                            where(VersionedEntity.Fields.state)
                                .is(ACTIVE)
                                .and(VersionedEntity.Fields.validFrom)
                                .lt(valuationDate)),
                        sort(
                            Sort.by(DESC, VersionedEntity.Fields.validFrom)
                                .and(Sort.by(DESC, VersionedEntity.Fields.modifiedAt))),
                        limit(1))
                    .as(COMPANY_SETTINGS),
                unwind(COMPANY_SETTINGS, true),

                // Determine applicable reporting currency
                addFields()
                    .addField(APPLICABLE_REPORTING_CURRENCY)
                    .withValue(
                        ConditionalOperators.Cond.when(
                                ComparisonOperators.Eq.valueOf(
                                        POSITIONAL_OPERATOR + ENTITY_SETTINGS + ".settingsType")
                                    .equalToValue(BESPOKE))
                            .then(POSITIONAL_OPERATOR + ENTITY_SETTINGS + ".reportingCurrency")
                            .otherwise(
                                ConditionalOperators.Cond.when(
                                        ComparisonOperators.Eq.valueOf(
                                                POSITIONAL_OPERATOR
                                                    + COMPANY_SETTINGS
                                                    + ".settingsType")
                                            .equalToValue(BESPOKE))
                                    .then(
                                        POSITIONAL_OPERATOR
                                            + COMPANY_SETTINGS
                                            + ".reportingCurrency")
                                    .otherwise(globalReportingCurrency)))
                    .build(),

                // get the latest record
                addFields()
                    .addField(MAX_RECORD_DATE)
                    .withValue(new Document("$max", POSITIONAL_OPERATOR + VALUES + ".recordDate"))
                    .build(),

                // Check for reporting currency mismatch
                addFields()
                    .addField(REPORTING_CURRENCY_MISMATCH)
                    .withValue(
                        ConditionalOperators.when(
                                ComparisonOperators.Ne.valueOf(
                                        ArrayOperators.ArrayElemAt.arrayOf(
                                                POSITIONAL_OPERATOR + VALUES + ".currency")
                                            .elementAt(
                                                ArrayOperators.IndexOfArray.arrayOf(
                                                        POSITIONAL_OPERATOR
                                                            + VALUES
                                                            + ".recordDate")
                                                    .indexOf(
                                                        POSITIONAL_OPERATOR + MAX_RECORD_DATE)))
                                    .notEqualTo(
                                        POSITIONAL_OPERATOR + APPLICABLE_REPORTING_CURRENCY))
                            .then(true)
                            .otherwise(false))
                    .build(),
                project().and("_id").as("navId").and(REPORTING_CURRENCY_MISMATCH).as("mismatch")),
            CompanyLegalEntityNav.class,
            Document.class);

    aggregation
        .getMappedResults()
        .forEach(
            doc -> {
              String navId = doc.getObjectId("navId").toHexString();
              Boolean mismatch = doc.getBoolean("mismatch");
              IpvDataNavValueView nav = relevantNavMap.get(navId);
              if (nav != null) {
                nav.setReportingCurrencyMismatch(mismatch);
              }
            });
  }

  public Stream<IpvDataProviderValueView> getValueViewsStream(
      @NonNull String groupId, @NonNull IpvDataProviderValueFilter filter, IpvDataType dataType) {

    String navLevel = getGlobalNavLevelSettings(BitemporalDate.newOf(filter.getValuationDate()));
    boolean isEntityLevelNav = navLevel.equals("ENTITY_LEVEL") && dataType == NAV;
    var filterCriteria =
        isEntityLevelNav ? filter.criteria(groupId) : filter.criteria(groupId, dataType);
    var collection = isEntityLevelNav ? CompanyLegalEntityNav.class : IpvDataValue.class;

    var operationBuilder = viewAggregations(filterCriteria);
    if (isEntityLevelNav) {
      operationBuilder.add(
          addFields().addField(IpvDataProviderValueView.Fields.provider).withValue("NAV").build());
    }
    var operations = operationBuilder.build();
    var options = AggregationOptions.builder().cursorBatchSize(1000).build();
    var aggregation = newAggregation(collection, operations).withOptions(options);
    return mongoOperations.aggregateStream(aggregation, IpvDataProviderValueView.class);
  }

  private boolean includeResolvedNavData(IpvDataProviderValueFilter filter) {
    return Boolean.TRUE.equals(filter.getResolved()) && !Boolean.TRUE.equals(filter.getArchived());
  }

  public Either<ErrorItem, IpvDataProviderValueView> getValueAtDate(
      @NonNull String groupId,
      @NonNull String key,
      @NonNull String provider,
      @NonNull LocalDate date) {

    Optional<IpvDataProviderValueView> result;

    if (NAV_PROVIDER_CODE.equals(provider)) {
      boolean isEntityLevelNav =
          getGlobalNavLevelSettings(BitemporalDate.newOf(date)).equals("ENTITY_LEVEL");
      var repository =
          isEntityLevelNav ? resolvedEntityNavViewRepository : resolvedTradeNavViewRepository;
      result = repository.findResolvedNavByGroupIdAndDateAndKey(groupId, date, key);
    } else {
      var criteria =
          where(BaseIpvDataValue.Fields.groupId)
              .is(groupId)
              .and(BaseIpvDataValue.Fields.key)
              .is(key)
              .and(IpvDataValue.Fields.provider)
              .is(provider)
              .and(DataValuesHolder.Fields.date)
              .is(date)
              .and(DataValuesHolder.Fields.archivedAt)
              .isNull();
      var aggregation = newAggregation(IpvDataValue.class, viewAggregations(criteria).build());
      result =
          Optional.ofNullable(
              mongoOperations
                  .aggregate(aggregation, IpvDataProviderValueView.class)
                  .getUniqueMappedResult());
    }

    return result
        .map(Either::<ErrorItem, IpvDataProviderValueView>right)
        .orElseGet(
            () ->
                Either.left(
                    OBJECT_NOT_FOUND.entity(
                        format(
                            "VD value not found for trade %s, provider %s, date %s",
                            key, provider, date))));
  }

  public Either<ErrorItem, IpvDataProviderValueView> getValueView(
      @NonNull String groupId, @NonNull String id, @NonNull BitemporalDate stateDate) {
    var operations = viewAggregations(valueCriteria(groupId, id, stateDate)).build();
    var aggregation = newAggregation(IpvDataValue.class, operations);
    var result =
        mongoOperations
            .aggregate(aggregation, IpvDataProviderValueView.class)
            .getUniqueMappedResult();
    return Eithers.cond(
        Objects.nonNull(result),
        OBJECT_NOT_FOUND.entity(format("VD value not found with id %s", id)),
        result);
  }

  public Optional<LocalDate> getLatestIpvDataDate(
      @NonNull String groupId, @NonNull LocalDate beforeDate, @NonNull BitemporalDate stateDate) {
    var criteria =
        where(BaseIpvDataValue.Fields.groupId)
            .is(groupId)
            .and(DataValuesHolder.Fields.date)
            .lt(beforeDate)
            .andOperator(validAtStateDateCriteria(stateDate, false))
            .and(joinPaths(DataValuesHolder.Fields.values, VersionedValue.Fields.recordDate))
            .lte(stateDate.getRecordDate());
    var operations =
        ImmutableList.<AggregationOperation>builder()
            .add(match(criteria))
            .add(sort(DESC, DataValuesHolder.Fields.date))
            .add(limit(1))
            .build();
    var aggregation = newAggregation(IpvDataValue.class, operations);
    var result = mongoOperations.aggregate(aggregation, Document.class).getUniqueMappedResult();
    return Optional.ofNullable(result).map(d -> getDate(d, DataValuesHolder.Fields.date));
  }

  public Long getValuesCount(@NonNull String groupId, @NonNull BitemporalDate stateDate) {
    return valuesCount(activeValuesCriteria(groupId, stateDate));
  }

  private long valuesCount(Criteria criteria) {
    return mongoOperations.count(query(criteria), IpvDataValue.class);
  }

  public Either<ErrorItem, EntityId> updateValue(
      @NonNull String id, @NonNull IpvDataProviderValueUpdateForm form) {
    return dataOperations.updateValue(id, ipvDataMapper.value(form));
  }

  public Either<ErrorItem, EntityId> archiveValue(@NonNull String id, @NonNull ArchiveForm form) {
    return dataOperations.archiveValue(id, form);
  }

  public boolean hasChangesAfter(@NonNull String groupId, @NonNull LocalDateTime timestamp) {
    return dataOperations.hasChangesAfter(
        where(BaseIpvDataValue.Fields.groupId).is(groupId), timestamp);
  }

  @Override
  public DataUpdateSummary updateForImport(
      DataValueUpdateResolver<IpvDataValueVersion, IpvDataValue> entities) {
    return dataOperations.updateForImport(entities);
  }

  public String getGlobalNavLevelSettings(BitemporalDate stateDate) {
    return exceptionManagementSettingsProvider.get().getNavLevel(stateDate);
  }
}
