package com.solum.xplain.core.curvegroup;

import static org.apache.commons.lang3.StringUtils.isEmpty;

import com.solum.xplain.core.utils.TenorUtils;
import org.jspecify.annotations.NullMarked;
import org.jspecify.annotations.Nullable;

@NullMarked
public interface ParsableTenor {

  /** Used for sort order - really needs to be something like nodeYearFraction */
  default double parsedTenor() {
    return TenorUtils.toYearFraction(getTenor());
  }

  @Nullable
  default Double nullSafeParsedTenor() {
    if (isEmpty(getTenor())) {
      return null;
    }
    return parsedTenor();
  }

  /**
   * Returns the tenor to use for parsing. For FRA nodes, this should return the sum of months of
   * the FRA period and the settlement period.
   *
   * @return for a 1Mx7M FRA, this should return 7M not 6M, for 2D+1,2 with 3M period we return 6M,
   *     9M
   */
  String getTenor();
}
