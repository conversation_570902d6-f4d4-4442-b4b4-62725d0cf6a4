package com.solum.xplain.core.config;

import com.opengamma.strata.pricer.fxopt.InterpolatedStrikeSmileDeltaTermStructure;
import com.solum.xplain.shared.datagrid.impl.redis.RedisJavaSerializationSupport;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@EnableCaching
@Configuration
public class CacheConfig {
  @Bean
  public RedisJavaSerializationSupport requiresJavaSerialization() {
    return new RedisJavaSerializationSupport(InterpolatedStrikeSmileDeltaTermStructure.class);
  }
}
