package com.solum.xplain.core.curvegroup.conventions.index;

import static com.opengamma.strata.product.swap.type.FixedInflationSwapConventions.CHF_FIXED_ZC_CH_CPI;
import static com.opengamma.strata.product.swap.type.FixedInflationSwapConventions.EUR_FIXED_ZC_EU_AI_CPI;
import static com.opengamma.strata.product.swap.type.FixedInflationSwapConventions.EUR_FIXED_ZC_EU_EXT_CPI;
import static com.opengamma.strata.product.swap.type.FixedInflationSwapConventions.EUR_FIXED_ZC_FR_CPI;
import static com.opengamma.strata.product.swap.type.FixedInflationSwapConventions.GBP_FIXED_ZC_GB_HICP;
import static com.opengamma.strata.product.swap.type.FixedInflationSwapConventions.GBP_FIXED_ZC_GB_RPI;
import static com.opengamma.strata.product.swap.type.FixedInflationSwapConventions.GBP_FIXED_ZC_GB_RPIX;
import static com.solum.xplain.core.curvegroup.conventions.index.ImmutableInflationCurveConvention.inflation;
import static java.util.List.of;

import com.opengamma.strata.basics.index.PriceIndices;
import com.opengamma.strata.product.swap.type.FixedInflationSwapConventions;
import com.solum.xplain.core.curvegroup.conventions.ClearingHouse;
import com.solum.xplain.extensions.index.ExtendedPriceIndices;
import com.solum.xplain.extensions.product.ExtendedFixedInflationSwapConventions;
import java.util.List;
import java.util.Optional;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class InflationCurveConventions {

  private static final ImmutableInflationCurveConvention CH_CPI =
      inflation(
          "CH CPI",
          PriceIndices.CH_CPI,
          CHF_FIXED_ZC_CH_CPI,
          ExtendedFixedInflationSwapConventions.CHF_FIXED_ZC_CH_CPI_30_360);

  private static final ImmutableInflationCurveConvention EU_AI_CPI =
      inflation(
          "EU AI CPI",
          PriceIndices.EU_AI_CPI,
          EUR_FIXED_ZC_EU_AI_CPI,
          ExtendedFixedInflationSwapConventions.EUR_FIXED_ZC_EU_AI_CPI_30_360);

  private static final ImmutableInflationCurveConvention EU_EXT_CPI =
      inflation(
          "EU EXT CPI",
          PriceIndices.EU_EXT_CPI,
          EUR_FIXED_ZC_EU_EXT_CPI,
          ExtendedFixedInflationSwapConventions.EUR_FIXED_ZC_EU_EXT_CPI_30_360,
          ExtendedFixedInflationSwapConventions.EUR_FIXED_ZC_EU_EXT_CPI_CLEARED,
          ExtendedFixedInflationSwapConventions.EUR_FIXED_ZC_EU_EXT_CPI_CLEARED_30_360);

  private static final ImmutableInflationCurveConvention FR_EXT_CPI =
      inflation(
          "FR EXT CPI",
          PriceIndices.FR_EXT_CPI,
          EUR_FIXED_ZC_FR_CPI,
          ExtendedFixedInflationSwapConventions.EUR_FIXED_ZC_FR_CPI_30_360,
          ExtendedFixedInflationSwapConventions.EUR_FIXED_ZC_FR_CPI_CLEARED,
          ExtendedFixedInflationSwapConventions.EUR_FIXED_ZC_FR_CPI_CLEARED_30_360);

  private static final ImmutableInflationCurveConvention GB_CPI =
      inflation(
          "GB CPI",
          ExtendedPriceIndices.GB_CPI,
          ExtendedFixedInflationSwapConventions.GBP_FIXED_ZC_GB_CPI,
          ExtendedFixedInflationSwapConventions.GBP_FIXED_ZC_GB_CPI_30_360);

  private static final ImmutableInflationCurveConvention GB_CPI_LCH =
      inflation(
          "GB CPI LCH",
          ClearingHouse.LCH,
          ExtendedPriceIndices.GB_CPI,
          ExtendedFixedInflationSwapConventions.GBP_FIXED_ZC_GB_CPI_CLEARED,
          ExtendedFixedInflationSwapConventions.GBP_FIXED_ZC_GB_CPI_CLEARED_30_360);

  private static final ImmutableInflationCurveConvention GB_HICP =
      inflation(
          "GB HICP",
          PriceIndices.GB_HICP,
          GBP_FIXED_ZC_GB_HICP,
          ExtendedFixedInflationSwapConventions.GBP_FIXED_ZC_GB_HICP_30_360);

  private static final ImmutableInflationCurveConvention GB_RPI =
      inflation(
          "GB RPI",
          PriceIndices.GB_RPI,
          GBP_FIXED_ZC_GB_RPI,
          ExtendedFixedInflationSwapConventions.GBP_FIXED_ZC_GB_RPI_30_360,
          ExtendedFixedInflationSwapConventions.GBP_FIXED_ZC_GB_RPI_CLEARED,
          ExtendedFixedInflationSwapConventions.GBP_FIXED_ZC_GB_RPI_CLEARED_30_360);

  private static final ImmutableInflationCurveConvention GB_RPIX =
      inflation(
          "GB RPIX",
          PriceIndices.GB_RPIX,
          GBP_FIXED_ZC_GB_RPIX,
          ExtendedFixedInflationSwapConventions.GBP_FIXED_ZC_GB_RPIX_30_360);

  private static final ImmutableInflationCurveConvention JP_CPI_EXF =
      inflation(
          "JP CPI EXF",
          PriceIndices.JP_CPI_EXF,
          FixedInflationSwapConventions.JPY_FIXED_ZC_JP_CPI,
          ExtendedFixedInflationSwapConventions.JPY_FIXED_ZC_JP_CPI_30_360,
          ExtendedFixedInflationSwapConventions.JPY_FIXED_ZC_JP_CPI_INTERPOLATED_NAME,
          ExtendedFixedInflationSwapConventions.JPY_FIXED_ZC_JP_CPI_INTERPOLATED_NAME_30_360);

  private static final ImmutableInflationCurveConvention US_CPI_U =
      inflation(
          "US CPI U",
          PriceIndices.US_CPI_U,
          FixedInflationSwapConventions.USD_FIXED_ZC_US_CPI,
          ExtendedFixedInflationSwapConventions.USD_FIXED_ZC_US_CPI_30_360);

  // LCH Clearing House Curves

  private static final ImmutableInflationCurveConvention US_CPI_U_LCH =
      inflation(
          "US CPI U LCH",
          ClearingHouse.LCH,
          PriceIndices.US_CPI_U,
          ExtendedFixedInflationSwapConventions.USD_FIXED_ZC_US_CPI,
          ExtendedFixedInflationSwapConventions.USD_FIXED_ZC_US_CPI_30_360,
          ExtendedFixedInflationSwapConventions.USD_FIXED_ZC_US_CPI_CLEARED,
          ExtendedFixedInflationSwapConventions.USD_FIXED_ZC_US_CPI_CLEARED_30_360);

  private static final ImmutableInflationCurveConvention GB_RPI_LCH =
      inflation(
          "GB RPI LCH",
          ClearingHouse.LCH,
          PriceIndices.GB_RPI,
          ExtendedFixedInflationSwapConventions.GBP_FIXED_ZC_GB_RPI_CLEARED,
          ExtendedFixedInflationSwapConventions.GBP_FIXED_ZC_GB_RPI_CLEARED_30_360);

  private static final ImmutableInflationCurveConvention FR_EXT_CPI_LCH =
      inflation(
          "FR EXT CPI LCH",
          ClearingHouse.LCH,
          PriceIndices.FR_EXT_CPI,
          ExtendedFixedInflationSwapConventions.EUR_FIXED_ZC_FR_CPI_CLEARED,
          ExtendedFixedInflationSwapConventions.EUR_FIXED_ZC_FR_CPI_CLEARED_30_360);

  private static final ImmutableInflationCurveConvention EU_EXT_CPI_LCH =
      inflation(
          "EU EXT CPI LCH",
          ClearingHouse.LCH,
          PriceIndices.EU_EXT_CPI,
          ExtendedFixedInflationSwapConventions.EUR_FIXED_ZC_EU_EXT_CPI_CLEARED,
          ExtendedFixedInflationSwapConventions.EUR_FIXED_ZC_EU_EXT_CPI_CLEARED_30_360);

  public static final List<IndexCurveConvention> INFLATION_CURVES =
      of(
          GB_RPI,
          EU_AI_CPI,
          EU_EXT_CPI,
          JP_CPI_EXF,
          FR_EXT_CPI,
          GB_CPI,
          GB_CPI_LCH,
          GB_HICP,
          CH_CPI,
          US_CPI_U,
          GB_RPIX,
          US_CPI_U_LCH,
          GB_RPI_LCH,
          FR_EXT_CPI_LCH,
          EU_EXT_CPI_LCH);

  public static Optional<IndexCurveConvention> findByIndexAndClearingHouse(
      String index, ClearingHouse clearingHouse) {
    return INFLATION_CURVES.stream()
        .filter(c -> c.getIndex().getName().equals(index))
        .filter(c -> ((ImmutableInflationCurveConvention) c).getClearingHouse() == clearingHouse)
        .findFirst();
  }
}
