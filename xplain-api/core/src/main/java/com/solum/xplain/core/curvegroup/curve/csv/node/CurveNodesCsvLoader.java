package com.solum.xplain.core.curvegroup.curve.csv.node;

import static com.solum.xplain.core.classifiers.CurveNodeTypes.FIXED_INFLATION_SWAP_NODE;
import static com.solum.xplain.core.classifiers.CurveNodeTypes.FRA_NODE;
import static com.solum.xplain.core.classifiers.CurveNodeTypes.FX_SWAP_NODE;
import static com.solum.xplain.core.classifiers.CurveNodeTypes.IBOR_FIXING_DEPOSIT_NODE;
import static com.solum.xplain.core.classifiers.CurveNodeTypes.TERM_DEPOSIT_NODE;
import static com.solum.xplain.core.classifiers.CurveNodeTypes.isImmNode;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.validateValue;
import static com.solum.xplain.core.common.validation.ValidPeriod.PeriodStyle.BRL;
import static com.solum.xplain.core.common.validation.ValidPeriod.PeriodStyle.MARKET_TENOR;
import static com.solum.xplain.core.common.validation.ValidPeriod.PeriodStyle.YM;
import static com.solum.xplain.core.curvegroup.conventions.ConventionalCurveConfigurations.lookupByName;
import static com.solum.xplain.core.curvegroup.curve.CurvesUtils.curveTenor;
import static com.solum.xplain.core.error.Error.IMPORT_ERROR;
import static com.solum.xplain.core.error.Error.PARSING_ERROR;
import static io.atlassian.fugue.Either.left;
import static io.atlassian.fugue.Either.right;
import static java.lang.String.format;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableSet;
import com.opengamma.strata.basics.date.MarketTenor;
import com.opengamma.strata.basics.date.Tenor;
import com.opengamma.strata.collect.io.CsvRow;
import com.opengamma.strata.collect.named.Named;
import com.solum.xplain.core.common.csv.DuplicateAction;
import com.solum.xplain.core.common.csv.ItemKey;
import com.solum.xplain.core.common.csv.NamedList;
import com.solum.xplain.core.common.csv.NodesCsvLoader;
import com.solum.xplain.core.common.validation.FraSettlementSupplier;
import com.solum.xplain.core.common.validation.SerialFutureSupplier;
import com.solum.xplain.core.common.validation.ValidPeriodValidator;
import com.solum.xplain.core.curvegroup.conventions.ConventionalCurveConvention;
import com.solum.xplain.core.curvegroup.conventions.CurveConvention;
import com.solum.xplain.core.curvegroup.curve.CurvesUtils;
import com.solum.xplain.core.curvegroup.curve.classifier.CurveType;
import com.solum.xplain.core.curvegroup.curve.entity.Curve;
import com.solum.xplain.core.curvegroup.curve.entity.CurveNodeInstrument;
import com.solum.xplain.core.curvegroup.curve.validation.CurveNodeValidationConstants;
import com.solum.xplain.core.curvegroup.curve.validation.RequireTnNodeValidator;
import com.solum.xplain.core.curvegroup.curve.value.CurveNodeForm;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import io.atlassian.fugue.Eithers;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

@Component
public class CurveNodesCsvLoader extends NodesCsvLoader<Curve, CurveNodeForm> {

  static final String CURVE_NAME_FIELD = "Curve Name";
  static final String TENOR_FIELD = "Tenor";
  static final String TYPE_FIELD = "Instrument Type";
  static final String CONVENTION_FIELD = "Convention";
  static final String FRA_SETTLEMENT = "FRA Settlement";
  static final String SERIAL_FUTURE = "Serial Future";

  protected List<String> getCsvFileHeaders() {
    return List.of(
        CURVE_NAME_FIELD, FRA_SETTLEMENT, SERIAL_FUTURE, TENOR_FIELD, TYPE_FIELD, CONVENTION_FIELD);
  }

  @Override
  protected String getCurveName(Curve curve) {
    return curve.getName();
  }

  @Override
  protected Either<ErrorItem, CurveNodeForm> parse(@NonNull CsvRow row) {
    try {
      String curveNameStr = row.getValue(CURVE_NAME_FIELD);
      return right(parse(row, curveNameStr));
    } catch (RuntimeException e) {
      return left(
          new ErrorItem(
              PARSING_ERROR,
              format("Error parsing line %d: %s", row.lineNumber(), e.getMessage())));
    }
  }

  @Override
  protected ItemKey getNodeKey(CurveNodeForm form) {
    return CurveNodeKey.from(form);
  }

  @Override
  protected Either<List<ErrorItem>, NamedList<CurveNodeForm>> validateCurveNodes(
      Curve curve, NamedList<CurveNodeForm> nodes, DuplicateAction action) {
    var builder = ImmutableList.<ErrorItem>builder();
    validateNodeTypes(curve, nodes).ifPresent(builder::add);
    validateTenors(curve, nodes, action).ifPresent(builder::add);
    var errors = builder.build();
    return Eithers.cond(errors.isEmpty(), errors, nodes);
  }

  private Optional<ErrorItem> validateNodeTypes(Curve curve, NamedList<CurveNodeForm> nodes) {
    var validTypes = validNodeTypes(curve);
    var allTypesValid = nodes.getItems().stream().allMatch(n -> validTypes.contains(n.getType()));

    if (!allTypesValid) {
      return Optional.of(unsupportedCurveNodeType(curve.getName(), validTypes));
    }
    return Optional.empty();
  }

  private Optional<ErrorItem> validateTenors(
      Curve curve, NamedList<CurveNodeForm> nodes, DuplicateAction action) {
    var tenorsBuilder = ImmutableSet.<String>builder();
    tenorsBuilder.addAll(nodes.getItems().stream().map(CurveNodeForm::getTenor).toList());
    if (!action.isDeleteAction()) {
      tenorsBuilder.addAll(
          Stream.ofNullable(curve.getNodes())
              .flatMap(Collection::stream)
              .map(CurveNodeInstrument::getTenor)
              .toList());
    }

    return RequireTnNodeValidator.validate(curve.getName(), tenorsBuilder.build());
  }

  private ErrorItem unsupportedCurveNodeType(String curveName, List<String> validTypes) {
    return new ErrorItem(
        IMPORT_ERROR,
        String.format(
            "Unsupported Node type for %s Curve found. Supported types are: [%s]",
            curveName, String.join(",", validTypes)));
  }

  private CurveNodeForm parse(@NonNull CsvRow row, String curveName) {
    String typeStr = row.getValue(TYPE_FIELD);
    String conventionStr = row.getValue(CONVENTION_FIELD);

    var node = new CurveNodeForm();
    var curveConventions = findCurveConventions(curveName);
    node.setType(validateType(typeStr, curveConventions));
    node.setConvention(validateConvention(typeStr, conventionStr, curveConventions));

    if (CurveNodeValidationConstants.isBrlConvention(conventionStr)) {
      String tenorStr = row.getValue(TENOR_FIELD).toUpperCase();
      node.setPeriod(validateValue(tenorStr, ValidPeriodValidator.isValid(tenorStr, BRL)));
    } else if (FRA_NODE.equalsIgnoreCase(typeStr)) {
      String fraSettlement = row.getValue(FRA_SETTLEMENT);
      node.setFraSettlement(validateValue(fraSettlement, new FraSettlementSupplier()));
      curveTenor(curveName).map(Tenor::toString).ifPresent(node::setPeriod);
    } else if (isImmNode(typeStr)) {
      String serialFuture = row.getValue(SERIAL_FUTURE);
      node.setSerialFuture(validateValue(serialFuture, new SerialFutureSupplier()));
      curveTenor(curveName).map(Tenor::toString).ifPresent(node::setPeriod);
    } else if (IBOR_FIXING_DEPOSIT_NODE.equalsIgnoreCase(typeStr)) {
      curveTenor(curveName).map(Tenor::toString).ifPresent(node::setPeriod);
    } else if (FX_SWAP_NODE.equalsIgnoreCase(typeStr)) {
      String tenorStr = row.getValue(TENOR_FIELD).toUpperCase();
      node.setPeriod(validateValue(tenorStr, ValidPeriodValidator.isValid(tenorStr, MARKET_TENOR)));
    } else if (FIXED_INFLATION_SWAP_NODE.equalsIgnoreCase(typeStr)) {
      String tenorStr = row.getValue(TENOR_FIELD).toUpperCase();
      node.setPeriod(validateValue(tenorStr, ValidPeriodValidator.isValid(tenorStr, YM)));
    } else if (TERM_DEPOSIT_NODE.equals(typeStr)) {
      String curveTenor =
          CurvesUtils.termDepositTenor(curveName, conventionStr)
              .map(MarketTenor::toString)
              .orElseThrow(() -> new IllegalArgumentException("Unsupported curve"));
      node.setPeriod(curveTenor);
    } else {
      String tenorStr = row.getValue(TENOR_FIELD).toUpperCase();
      node.setPeriod(validateValue(tenorStr, ValidPeriodValidator.isValid(tenorStr)));
    }
    return node;
  }

  private String validateType(String type, List<ConventionalCurveConvention> curveConventions) {
    var types =
        curveConventions.stream()
            .map(CurveConvention::getAllPermissibleNodeTypes)
            .flatMap(Collection::stream)
            .toList();

    return validateValue(type, types);
  }

  private String validateConvention(
      String nodeType, String convention, List<ConventionalCurveConvention> curveConventions) {
    var nodeConventions =
        curveConventions.stream()
            .map(v -> v.nodeTypeConventions(nodeType))
            .flatMap(Collection::stream)
            .map(Named::getName)
            .toList();

    return validateValue(convention, nodeConventions);
  }

  private List<String> validNodeTypes(Curve curve) {
    return List.copyOf(
        findCurveConvention(curve.getName(), curve.getCurveType()).getAllPermissibleNodeTypes());
  }

  private CurveConvention findCurveConvention(String curveName, CurveType type) {
    return lookupByName(curveName, type)
        .orElseThrow(() -> new RuntimeException("Unsupported curve name: " + curveName));
  }

  private List<ConventionalCurveConvention> findCurveConventions(String curveName) {
    var curves = lookupByName(curveName);
    if (curves.isEmpty()) {
      throw new IllegalArgumentException("Unsupported curve name: " + curveName);
    }
    return curves;
  }
}
