// Generic base repository interface
package com.solum.xplain.core.ipv.data.repository;

import com.solum.xplain.core.common.ScrollRequest;
import com.solum.xplain.core.common.ScrollableEntry;
import com.solum.xplain.core.common.ScrollableEntrySupport;
import com.solum.xplain.core.ipv.data.value.IpvDataProviderValueView;
import com.solum.xplain.core.spring.mongo.XplainDefaultAggregations;
import com.solum.xplain.shared.utils.filter.TableFilter;
import java.time.LocalDate;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.repository.NoRepositoryBean;
import org.springframework.data.repository.Repository;
import org.springframework.data.util.Streamable;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

/**
 * Generic base repository for IPV resolved data provider value views.
 *
 * @param <T> The entity type (e.g., IpvResolvedDataProviderValueWithTradeNavView,
 *     IpvResolvedDataProviderValueWithEntityNavView)
 */
@NoRepositoryBean
@XplainDefaultAggregations
public interface IpvResolvedDataProviderValueViewBaseRepository<T> extends Repository<T, String> {

  Long countAllByGroupIdAndDate(
      @NonNull String groupId, @NonNull LocalDate valuationDate, @NonNull TableFilter tableFilter);

  Slice<IpvDataProviderValueView> findAllByGroupIdAndDate(
      @NonNull String groupId,
      @NonNull LocalDate valuationDate,
      @NonNull TableFilter tableFilter,
      @Nullable Pageable scrollRequest);

  Streamable<IpvDataProviderValueView> findAllByGroupIdAndDate(
      @NonNull String groupId, @NonNull LocalDate valuationDate, @NonNull TableFilter tableFilter);

  default ScrollableEntry<IpvDataProviderValueView> getValueViews(
      @NonNull String groupId,
      @NonNull LocalDate valuationDate,
      @NonNull TableFilter tableFilter,
      @NonNull ScrollRequest scrollRequest) {

    return ScrollableEntrySupport.getScrollableEntry(
        () -> countAllByGroupIdAndDate(groupId, valuationDate, tableFilter),
        () -> findAllByGroupIdAndDate(groupId, valuationDate, tableFilter, scrollRequest),
        scrollRequest);
  }
}
