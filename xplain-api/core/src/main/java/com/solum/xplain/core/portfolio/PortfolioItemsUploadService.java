package com.solum.xplain.core.portfolio;

import static com.solum.xplain.core.common.CollectionUtils.chunked;
import static com.solum.xplain.core.common.CollectionUtils.toSizedList;
import static com.solum.xplain.core.common.csv.ImportDescriptionUtils.description;
import static com.solum.xplain.core.common.csv.ImportErrorUtils.stateChanged;
import static com.solum.xplain.core.common.csv.LargeImportUtils.validateDuplicateItems;
import static com.solum.xplain.core.common.csv.LargeImportUtils.validateExistingItemFutureVersions;
import static com.solum.xplain.core.common.csv.LargeImportUtils.validateMissingItemFutureVersions;
import static com.solum.xplain.core.common.csv.LargeImportUtils.validateMissingItems;
import static com.solum.xplain.core.common.csv.LargeImportUtils.validateViableNewVersions;
import static io.atlassian.fugue.Either.right;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toSet;
import static org.apache.commons.lang3.BooleanUtils.isTrue;

import com.solum.xplain.core.audit.AuditEntryService;
import com.solum.xplain.core.audit.entity.AuditEntry;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.csv.CompositeVersionedImportItems;
import com.solum.xplain.core.common.csv.CsvLoader;
import com.solum.xplain.core.common.csv.CsvParserResult;
import com.solum.xplain.core.common.csv.ImportActionSummary;
import com.solum.xplain.core.common.csv.LargeVersionedImportItems;
import com.solum.xplain.core.common.csv.VersionedImportItems;
import com.solum.xplain.core.common.versions.embedded.ResolvableEmbeddedVersionValue;
import com.solum.xplain.core.common.versions.embedded.update.EntityForUpdate;
import com.solum.xplain.core.csv.LargeFileImporter;
import com.solum.xplain.core.csv.ValidationResponse;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.error.ErrorItem.ListOfErrors;
import com.solum.xplain.core.portfolio.csv.PortfolioItemCsvForm;
import com.solum.xplain.core.portfolio.event.PortfolioUpdated;
import com.solum.xplain.core.portfolio.form.TradeImportOptions;
import com.solum.xplain.core.portfolio.repository.PortfolioItemWriteRepository;
import com.solum.xplain.core.portfolio.repository.PortfolioRepository;
import com.solum.xplain.core.portfolio.trade.TradeValue;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map.Entry;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.AllArgsConstructor;
import org.jspecify.annotations.NullMarked;
import org.jspecify.annotations.Nullable;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@NullMarked
public class PortfolioItemsUploadService {

  private static final String OBJECT_NAME = "Trade";
  private final PortfolioItemWriteRepository portfolioItemWriteRepository;
  private final PortfolioRepository portfolioRepository;
  private final AuditEntryService auditEntryService;
  private final ApplicationEventPublisher eventPublisher;

  private String getCollection() {
    return PortfolioItem.PORTFOLIO_ITEM_COLLECTION;
  }

  public Either<List<ErrorItem>, ValidationResponse> validate(
      CsvLoader<PortfolioItemCsvForm> loader, byte[] bytes, TradeImportOptions importOptions) {
    var stateDate = importOptions.getStateDate();
    return loader
        .parse(bytes, importOptions.parsingMode())
        .map(CsvParserResult::getParsedLines)
        .map(
            f ->
                isTrue(importOptions.getOnlyAllocationTrades())
                    ? allocationImportItems(f, stateDate, importOptions.getReferenceTradeId())
                    : groupedByPortfolioImportItems(f, stateDate))
        .flatMap(
            importItemsGroups ->
                importItemsGroups
                    .map(group -> validateItemsGroup(stateDate, group))
                    .flatMap(Collection::stream)
                    .collect(collectingAndThen(toList(), l -> right(new ValidationResponse(l)))));
  }

  private List<ErrorItem> validateItemsGroup(
      LocalDate stateDate,
      VersionedImportItems<TradeValue, PortfolioItemUniqueKey, PortfolioItemEntity> importItems) {

    final List<ErrorItem> errors = new ArrayList<>();
    validateExistingItemFutureVersions(importItems, stateDate).ifPresent(errors::add);
    validateMissingItemFutureVersions(importItems, stateDate).ifPresent(errors::add);
    validateViableNewVersions(importItems, stateDate).ifPresent(errors::add);
    validateDuplicateItems(importItems.getDuplicateActiveItemsKeys()).ifPresent(errors::add);
    validateMissingItems(importItems.getSpareItemsKeys()).ifPresent(errors::add);
    return errors;
  }

  public Either<List<ErrorItem>, List<EntityId>> importTrades(
      CsvLoader<PortfolioItemCsvForm> loader, byte[] bytes, TradeImportOptions importOptions) {
    return loader
        .parse(bytes, importOptions.parsingMode())
        .flatMap(parsed -> importTrades(parsed, importOptions).leftMap(ListOfErrors::from));
  }

  private Either<ErrorItem, List<EntityId>> importTrades(
      CsvParserResult<PortfolioItemCsvForm> parserResult, TradeImportOptions importOptions) {
    var forms = parserResult.getParsedLines();
    var portfolioIds = forms.stream().map(e -> e.getUniqueKey().getPortfolioId()).collect(toSet());
    if (portfolioRepository.hasChanges(portfolioIds, importOptions.getValidationTimestamp())) {
      return Either.left(stateChanged("Portfolios"));
    }

    var stateDate = importOptions.getStateDate();
    Stream<VersionedImportItems<TradeValue, PortfolioItemUniqueKey, PortfolioItemEntity>>
        importItemsGroups =
            isTrue(importOptions.getOnlyAllocationTrades())
                ? allocationImportItems(forms, stateDate, importOptions.getReferenceTradeId())
                : groupedByPortfolioImportItems(forms, stateDate);

    var results =
        chunked(importItemsGroups)
            .map(
                importItems -> {
                  var composite = new CompositeVersionedImportItems<>(importItems);
                  return new LargeFileImporter<
                          TradeValue, PortfolioItemUniqueKey, PortfolioItemEntity>(
                          importOptions, portfolioItemWriteRepository)
                      .importItems(composite);
                })
            .flatMap(Collection::stream)
            .toList();
    logImport(results, parserResult.getWarnings());
    markPortfolioUpdate(portfolioIds);
    return portfolioIds.stream()
        .map(EntityId::new)
        .collect(Collectors.collectingAndThen(toList(), Either::right));
  }

  private void markPortfolioUpdate(Set<String> portfolioIds) {
    LocalDateTime importDate = LocalDateTime.now();
    eventPublisher.publishEvent(new PortfolioUpdated(portfolioIds, importDate));
  }

  private void logImport(List<ImportActionSummary> summaries, List<ErrorItem> logs) {
    var changesCount = summaries.stream().mapToInt(ImportActionSummary::getChangesCount).sum();
    var description = description(OBJECT_NAME, 0, logs.size(), changesCount);
    auditEntryService.newEntryWithLogs(AuditEntry.of(getCollection(), description), logs);
  }

  private Stream<VersionedImportItems<TradeValue, PortfolioItemUniqueKey, PortfolioItemEntity>>
      groupedByPortfolioImportItems(List<PortfolioItemCsvForm> forms, LocalDate stateDate) {
    var byPortfolioIds = forms.stream().collect(groupingBy(f -> f.getUniqueKey().getPortfolioId()));

    return chunked(byPortfolioIds.entrySet().stream())
        .flatMap(
            chunkedPortfolios -> {
              // These are estimates to avoid excessive resizing of collections during grouping.
              var estimatedMapSize = chunkedPortfolios.size();
              var estimatedItemPerPortfolio =
                  chunkedPortfolios.stream()
                      .map(Entry::getValue)
                      .mapToInt(List::size)
                      .max()
                      .orElse(10);

              var allIds = chunkedPortfolios.stream().map(Entry::getKey).collect(toSet());
              var localEntitiesForUpdate =
                  portfolioItemWriteRepository
                      .streamEntitiesForUpdate(allIds, stateDate)
                      .collect(
                          groupingBy(
                              item -> item.getEntity().getPortfolioId(),
                              () -> new HashMap<>(estimatedMapSize),
                              toSizedList(estimatedItemPerPortfolio)));

              return chunkedPortfolios.stream()
                  .map(
                      byPortfolioId -> {
                        var entitiesForUpdate = localEntitiesForUpdate.get(byPortfolioId.getKey());
                        return importItems(byPortfolioId.getValue(), entitiesForUpdate);
                      });
            });
  }

  private Stream<VersionedImportItems<TradeValue, PortfolioItemUniqueKey, PortfolioItemEntity>>
      allocationImportItems(
          List<PortfolioItemCsvForm> forms, LocalDate stateDate, @Nullable String refTradeId) {
    return Stream.of(
        importItems(
            forms,
            portfolioItemWriteRepository.entitiesForUpdate(null, stateDate, true, refTradeId)));
  }

  private VersionedImportItems<TradeValue, PortfolioItemUniqueKey, PortfolioItemEntity> importItems(
      List<PortfolioItemCsvForm> forms,
      List<EntityForUpdate<TradeValue, PortfolioItemEntity>> existingItems) {
    return LargeVersionedImportItems
        .<TradeValue, PortfolioItemUniqueKey, PortfolioItemEntity>builder()
        .existingItems(existingItems)
        .existingItemToKeyFn(e -> e.getEntity().uniqueKey())
        .importItems(List.copyOf(forms))
        .importItemToKeyFn(ResolvableEmbeddedVersionValue::getEntityId)
        .newEntityFn(PortfolioItemEntity::new)
        .build();
  }
}
