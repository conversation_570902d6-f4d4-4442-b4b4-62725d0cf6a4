package com.solum.xplain.core.ipv.data;

import static com.solum.xplain.core.common.versions.State.ACTIVE;
import static com.solum.xplain.core.utils.PathUtils.joinPaths;
import static com.solum.xplain.core.utils.ReflectionUtils.propertyName;
import static com.solum.xplain.core.utils.mongo.MongoVariables.VALUE_PREFIX;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.addFields;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.limit;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.lookup;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.match;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.project;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.replaceRoot;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.unwind;
import static org.springframework.data.mongodb.core.aggregation.BooleanOperators.And.and;
import static org.springframework.data.mongodb.core.aggregation.VariableOperators.Let.ExpressionVariable.newVariable;
import static org.springframework.data.mongodb.core.query.Criteria.expr;
import static org.springframework.data.mongodb.core.query.Criteria.where;

import com.solum.xplain.core.common.versions.VersionedEntity;
import com.solum.xplain.core.common.versions.daterange.DateRangeVersionValidity;
import com.solum.xplain.core.common.versions.daterange.DateRangeVersionedEntity;
import com.solum.xplain.core.datavalue.DataValuesHolder;
import com.solum.xplain.core.datavalue.VersionedValue;
import com.solum.xplain.core.ipv.data.entity.BaseIpvDataValue;
import com.solum.xplain.core.ipv.data.entity.IpvDataValue;
import com.solum.xplain.core.ipv.data.entity.IpvDataValueVersion;
import com.solum.xplain.core.ipv.data.entity.IpvResolvedDataProviderValueWithEntityNavView;
import com.solum.xplain.core.ipv.data.entity.IpvResolvedDataProviderValueWithTradeNavView;
import com.solum.xplain.core.ipv.data.value.IpvDataNavValueView;
import com.solum.xplain.core.ipv.data.value.IpvDataProviderValueView;
import com.solum.xplain.core.ipv.nav.entity.CompanyLegalEntityNav;
import com.solum.xplain.core.ipv.nav.entity.NavVersion;
import com.solum.xplain.core.portfolio.PortfolioItem;
import com.solum.xplain.core.portfolio.VersionedTradeEntity;
import com.solum.xplain.core.portfolio.trade.TradeDetails;
import com.solum.xplain.core.portfolio.trade.TradeInfoDetails;
import com.solum.xplain.core.providers.DataProvider;
import com.solum.xplain.core.utils.mongo.MongoVariables;
import lombok.RequiredArgsConstructor;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.AggregationPipeline;
import org.springframework.data.mongodb.core.aggregation.ArrayOperators;
import org.springframework.data.mongodb.core.aggregation.ComparisonOperators;
import org.springframework.data.mongodb.core.aggregation.StringOperators;
import org.springframework.stereotype.Component;

/**
 * Defines database views for IpvData. These will normally be used during a Mongock startup to
 * create the views but can also be called manually to create views for integration tests.
 */
@Component
@RequiredArgsConstructor
public class IpvDataViews {
  public static final String IPV_DATA_VALUE_WITH_TRADE_NAV = "v_ipvDataValueWithTradeNav";
  public static final String IPV_DATA_VALUE_WITH_ENTITY_NAV = "v_ipvDataValueWithEntityNav";
  private static final String VDK_VAR = "vdk";
  private static final String DATE_VAR = "date";
  private static final String NAV_VALUES = "navValues";
  private static final String VALUE = "value";
  private static final String NAV_VALUE = "navValue";
  private static final String VALIDITIES_VAR = "validities";

  private final MongoTemplate mongoTemplate;

  public void createViews() {
    mongoTemplate.createView(
        IPV_DATA_VALUE_WITH_TRADE_NAV,
        IpvDataValue.class,
        match(
            where(DataValuesHolder.Fields.archivedAt)
                .isNull()
                .and(BaseIpvDataValue.Fields.resolved)
                .is(true)
                .and(IpvDataValue.Fields.provider)
                .ne(DataProvider.NAV_PROVIDER_CODE)),
        lookup()
            .from(IpvDataValue.COLLECTION_NAME)
            .localField(BaseIpvDataValue.Fields.groupId)
            .foreignField(BaseIpvDataValue.Fields.groupId)
            .let(
                newVariable(VDK_VAR).forField(BaseIpvDataValue.Fields.key),
                newVariable(DATE_VAR).forField(DataValuesHolder.Fields.date))
            .pipeline(
                match(
                    where(DataValuesHolder.Fields.archivedAt)
                        .isNull()
                        .and(IpvDataValue.Fields.provider)
                        .is(DataProvider.NAV_PROVIDER_CODE)),
                match(
                    expr(
                        and(
                            ComparisonOperators.valueOf(DataValuesHolder.Fields.date)
                                .equalTo(VALUE_PREFIX + DATE_VAR),
                            ComparisonOperators.valueOf(BaseIpvDataValue.Fields.key)
                                .equalTo(VALUE_PREFIX + VDK_VAR)))),
                limit(1),
                replaceRoot(ArrayOperators.arrayOf(DataValuesHolder.Fields.values).elementAt(-1)))
            .as(NAV_VALUES));

    mongoTemplate.createView(
        IpvResolvedDataProviderValueWithTradeNavView.VIEW_NAME,
        IPV_DATA_VALUE_WITH_TRADE_NAV,
        AggregationPipeline.of(
            addFields()
                .addField(VALUE)
                .withValue(ArrayOperators.arrayOf(DataValuesHolder.Fields.values).elementAt(-1))
                .addField(NAV_VALUE)
                .withValue(ArrayOperators.arrayOf(NAV_VALUES).elementAt(-1))
                .addField(DataValuesHolder.Fields.values)
                .withValue(MongoVariables.REMOVE_DIRECTIVE)
                .addField(NAV_VALUES)
                .withValue(MongoVariables.REMOVE_DIRECTIVE)
                .build(),
            lookup()
                .from(PortfolioItem.PORTFOLIO_ITEM_COLLECTION)
                .localField(BaseIpvDataValue.Fields.key)
                .foreignField(PortfolioItem.Fields.valuationDataKey)
                .let(newVariable(DATE_VAR).forField(DataValuesHolder.Fields.date))
                .pipeline(
                    match(
                        where(VersionedEntity.Fields.state)
                            .is(ACTIVE)
                            .and(PortfolioItem.Fields.portfolioArchivedAt)
                            .isNull()),
                    match(
                        expr(
                            and(
                                ComparisonOperators.valueOf(
                                        DateRangeVersionedEntity.Fields.validFrom)
                                    .lessThanEqualTo(VALUE_PREFIX + DATE_VAR),
                                ComparisonOperators.valueOf(
                                        ArrayOperators.Size.lengthOfArray(
                                            ArrayOperators.Filter.filter(
                                                    DateRangeVersionedEntity.Fields.validities)
                                                .as(VALIDITIES_VAR)
                                                .by(
                                                    and(
                                                        ComparisonOperators.valueOf(
                                                                joinPaths(
                                                                    VALUE_PREFIX + VALIDITIES_VAR,
                                                                    DateRangeVersionValidity.Fields
                                                                        .validTo))
                                                            .greaterThan(VALUE_PREFIX + DATE_VAR),
                                                        ComparisonOperators.valueOf(
                                                                joinPaths(
                                                                    VALUE_PREFIX + VALIDITIES_VAR,
                                                                    DateRangeVersionValidity.Fields
                                                                        .recordFrom))
                                                            .lessThanEqualTo("$$NOW"),
                                                        ComparisonOperators.valueOf(
                                                                joinPaths(
                                                                    VALUE_PREFIX + VALIDITIES_VAR,
                                                                    DateRangeVersionValidity.Fields
                                                                        .recordTo))
                                                            .greaterThan("$$NOW")))))
                                    .greaterThanValue(0)))),
                    limit(1))
                .as(PortfolioItem.PORTFOLIO_ITEM_COLLECTION),
            unwind(PortfolioItem.PORTFOLIO_ITEM_COLLECTION, true),
            project()
                .and(BaseIpvDataValue.Fields.groupId)
                .as(IpvDataProviderValueView.Fields.groupId)
                .and(DataValuesHolder.Fields.date)
                .as(IpvDataProviderValueView.Fields.date)
                .and(BaseIpvDataValue.Fields.key)
                .as(IpvDataProviderValueView.Fields.key)
                .and(IpvDataValue.Fields.provider)
                .as(IpvDataProviderValueView.Fields.provider)
                .and(BaseIpvDataValue.Fields.resolved)
                .as(IpvDataProviderValueView.Fields.resolved)
                .and(propertyName(VALUE, VersionedValue.Fields.value))
                .as(IpvDataProviderValueView.Fields.value)
                .and(propertyName(VALUE, IpvDataValueVersion.Fields.delta))
                .as(IpvDataProviderValueView.Fields.delta)
                .and(propertyName(VALUE, IpvDataValueVersion.Fields.vega))
                .as(IpvDataProviderValueView.Fields.vega)
                .and(propertyName(VALUE, IpvDataValueVersion.Fields.gamma))
                .as(IpvDataProviderValueView.Fields.gamma)
                .and(propertyName(VALUE, IpvDataValueVersion.Fields.theta))
                .as(IpvDataProviderValueView.Fields.theta)
                .and(propertyName(VALUE, IpvDataValueVersion.Fields.rho))
                .as(IpvDataProviderValueView.Fields.rho)
                .and(propertyName(VALUE, IpvDataValueVersion.Fields.parRate))
                .as(IpvDataProviderValueView.Fields.parRate)
                .and(propertyName(VALUE, IpvDataValueVersion.Fields.spotRate))
                .as(IpvDataProviderValueView.Fields.spotRate)
                .and(propertyName(VALUE, IpvDataValueVersion.Fields.impliedVol))
                .as(IpvDataProviderValueView.Fields.impliedVol)
                .and(propertyName(VALUE, IpvDataValueVersion.Fields.atmImpliedVol))
                .as(IpvDataProviderValueView.Fields.atmImpliedVol)
                .and(propertyName(VALUE, IpvDataValueVersion.Fields.realisedVol))
                .as(IpvDataProviderValueView.Fields.realisedVol)
                .and(propertyName(VALUE, IpvDataValueVersion.Fields.fairVol))
                .as(IpvDataProviderValueView.Fields.fairVol)
                .and(propertyName(VALUE, NavVersion.Fields.currency))
                .as(IpvDataNavValueView.Fields.currency)
                .and(propertyName(VALUE, VersionedValue.Fields.comment))
                .as(IpvDataProviderValueView.Fields.comment)
                .and(propertyName(NAV_VALUE, VersionedValue.Fields.value))
                .as(IpvDataProviderValueView.Fields.navValue)
                .and(propertyName(NAV_VALUE, NavVersion.Fields.currency))
                .as(IpvDataProviderValueView.Fields.navCurrency)
                .and(
                    joinPaths(
                        PortfolioItem.PORTFOLIO_ITEM_COLLECTION,
                        VersionedTradeEntity.Fields.tradeDetails,
                        TradeDetails.Fields.info,
                        TradeInfoDetails.Fields.tradeCurrency))
                .as(IpvDataProviderValueView.Fields.tradeCcy)),
        null);

    mongoTemplate.createView(
        IPV_DATA_VALUE_WITH_ENTITY_NAV,
        IpvDataValue.class,
        match(
            where(DataValuesHolder.Fields.archivedAt)
                .isNull()
                .and(BaseIpvDataValue.Fields.resolved)
                .is(true)
                .and(IpvDataValue.Fields.provider)
                .ne(DataProvider.NAV_PROVIDER_CODE)),
        lookup()
            .from(CompanyLegalEntityNav.COLLECTION_NAME)
            .localField(BaseIpvDataValue.Fields.groupId)
            .foreignField(BaseIpvDataValue.Fields.groupId)
            .let(
                newVariable(VDK_VAR).forField(BaseIpvDataValue.Fields.key),
                newVariable(DATE_VAR).forField(DataValuesHolder.Fields.date))
            .pipeline(
                match(
                    where(DataValuesHolder.Fields.archivedAt)
                        .isNull()
                        .and(BaseIpvDataValue.Fields.resolved)
                        .is(true)),
                match(
                    expr(
                        and(
                            ComparisonOperators.valueOf(DataValuesHolder.Fields.date)
                                .equalTo(VALUE_PREFIX + DATE_VAR),
                            StringOperators.valueOf(VALUE_PREFIX + VDK_VAR)
                                .regexMatch(
                                    StringOperators.Concat.stringValue("^")
                                        .concatValueOf(BaseIpvDataValue.Fields.key))))),
                limit(1),
                replaceRoot(ArrayOperators.arrayOf(DataValuesHolder.Fields.values).elementAt(-1)))
            .as(NAV_VALUES));

    mongoTemplate.createView(
        IpvResolvedDataProviderValueWithEntityNavView.VIEW_NAME,
        IPV_DATA_VALUE_WITH_ENTITY_NAV,
        AggregationPipeline.of(
            addFields()
                .addField(VALUE)
                .withValue(ArrayOperators.arrayOf(DataValuesHolder.Fields.values).elementAt(-1))
                .addField(NAV_VALUE)
                .withValue(ArrayOperators.arrayOf(NAV_VALUES).elementAt(-1))
                .addField(DataValuesHolder.Fields.values)
                .withValue(MongoVariables.REMOVE_DIRECTIVE)
                .addField(NAV_VALUES)
                .withValue(MongoVariables.REMOVE_DIRECTIVE)
                .build(),
            lookup()
                .from(PortfolioItem.PORTFOLIO_ITEM_COLLECTION)
                .localField(BaseIpvDataValue.Fields.key)
                .foreignField(PortfolioItem.Fields.valuationDataKey)
                .let(newVariable(DATE_VAR).forField(DataValuesHolder.Fields.date))
                .pipeline(
                    match(
                        where(VersionedEntity.Fields.state)
                            .is(ACTIVE)
                            .and(PortfolioItem.Fields.portfolioArchivedAt)
                            .isNull()),
                    match(
                        expr(
                            and(
                                ComparisonOperators.valueOf(
                                        DateRangeVersionedEntity.Fields.validFrom)
                                    .lessThanEqualTo(VALUE_PREFIX + DATE_VAR),
                                ComparisonOperators.valueOf(
                                        ArrayOperators.Size.lengthOfArray(
                                            ArrayOperators.Filter.filter(
                                                    DateRangeVersionedEntity.Fields.validities)
                                                .as(VALIDITIES_VAR)
                                                .by(
                                                    and(
                                                        ComparisonOperators.valueOf(
                                                                joinPaths(
                                                                    VALUE_PREFIX + VALIDITIES_VAR,
                                                                    DateRangeVersionValidity.Fields
                                                                        .validTo))
                                                            .greaterThan(VALUE_PREFIX + DATE_VAR),
                                                        ComparisonOperators.valueOf(
                                                                joinPaths(
                                                                    VALUE_PREFIX + VALIDITIES_VAR,
                                                                    DateRangeVersionValidity.Fields
                                                                        .recordFrom))
                                                            .lessThanEqualTo("$$NOW"),
                                                        ComparisonOperators.valueOf(
                                                                joinPaths(
                                                                    VALUE_PREFIX + VALIDITIES_VAR,
                                                                    DateRangeVersionValidity.Fields
                                                                        .recordTo))
                                                            .greaterThan("$$NOW")))))
                                    .greaterThanValue(0)))),
                    limit(1))
                .as(PortfolioItem.PORTFOLIO_ITEM_COLLECTION),
            unwind(PortfolioItem.PORTFOLIO_ITEM_COLLECTION, true),
            project()
                .and(BaseIpvDataValue.Fields.groupId)
                .as(IpvDataProviderValueView.Fields.groupId)
                .and(DataValuesHolder.Fields.date)
                .as(IpvDataProviderValueView.Fields.date)
                .and(BaseIpvDataValue.Fields.key)
                .as(IpvDataProviderValueView.Fields.key)
                .and(IpvDataValue.Fields.provider)
                .as(IpvDataProviderValueView.Fields.provider)
                .and(BaseIpvDataValue.Fields.resolved)
                .as(IpvDataProviderValueView.Fields.resolved)
                .and(propertyName(VALUE, VersionedValue.Fields.value))
                .as(IpvDataProviderValueView.Fields.value)
                .and(propertyName(VALUE, IpvDataValueVersion.Fields.delta))
                .as(IpvDataProviderValueView.Fields.delta)
                .and(propertyName(VALUE, IpvDataValueVersion.Fields.vega))
                .as(IpvDataProviderValueView.Fields.vega)
                .and(propertyName(VALUE, IpvDataValueVersion.Fields.gamma))
                .as(IpvDataProviderValueView.Fields.gamma)
                .and(propertyName(VALUE, IpvDataValueVersion.Fields.theta))
                .as(IpvDataProviderValueView.Fields.theta)
                .and(propertyName(VALUE, IpvDataValueVersion.Fields.rho))
                .as(IpvDataProviderValueView.Fields.rho)
                .and(propertyName(VALUE, IpvDataValueVersion.Fields.parRate))
                .as(IpvDataProviderValueView.Fields.parRate)
                .and(propertyName(VALUE, IpvDataValueVersion.Fields.spotRate))
                .as(IpvDataProviderValueView.Fields.spotRate)
                .and(propertyName(VALUE, IpvDataValueVersion.Fields.impliedVol))
                .as(IpvDataProviderValueView.Fields.impliedVol)
                .and(propertyName(VALUE, IpvDataValueVersion.Fields.atmImpliedVol))
                .as(IpvDataProviderValueView.Fields.atmImpliedVol)
                .and(propertyName(VALUE, IpvDataValueVersion.Fields.realisedVol))
                .as(IpvDataProviderValueView.Fields.realisedVol)
                .and(propertyName(VALUE, IpvDataValueVersion.Fields.fairVol))
                .as(IpvDataProviderValueView.Fields.fairVol)
                .and(propertyName(VALUE, NavVersion.Fields.currency))
                .as(IpvDataNavValueView.Fields.currency)
                .and(propertyName(VALUE, VersionedValue.Fields.comment))
                .as(IpvDataProviderValueView.Fields.comment)
                .and(propertyName(NAV_VALUE, VersionedValue.Fields.value))
                .as(IpvDataProviderValueView.Fields.navValue)
                .and(propertyName(NAV_VALUE, NavVersion.Fields.currency))
                .as(IpvDataProviderValueView.Fields.navCurrency)
                .and(
                    joinPaths(
                        PortfolioItem.PORTFOLIO_ITEM_COLLECTION,
                        VersionedTradeEntity.Fields.tradeDetails,
                        TradeDetails.Fields.info,
                        TradeInfoDetails.Fields.tradeCurrency))
                .as(IpvDataProviderValueView.Fields.tradeCcy)),
        null);
  }

  public void dropViews() {
    mongoTemplate.dropCollection(IPV_DATA_VALUE_WITH_TRADE_NAV);
    mongoTemplate.dropCollection(IPV_DATA_VALUE_WITH_ENTITY_NAV);
    mongoTemplate.dropCollection(IpvResolvedDataProviderValueWithTradeNavView.class);
    mongoTemplate.dropCollection(IpvResolvedDataProviderValueWithEntityNavView.class);
  }
}
