package com.solum.xplain.core.company.repository;

import static com.solum.xplain.core.common.filter.VersionedEntityFilter.active;
import static com.solum.xplain.core.common.value.FutureVersionsAction.DELETE;
import static com.solum.xplain.core.common.value.NewVersionFormV2.ROOT_DATE;
import static com.solum.xplain.core.company.CompanySettingsType.BESPOKE;
import static com.solum.xplain.core.company.CompanySettingsType.DEFAULT;
import static com.solum.xplain.core.company.value.CompanyValuationSettingsResolver.applicableLegalEntitySettings;
import static com.solum.xplain.core.utils.ReflectionUtils.propertyName;
import static java.util.function.Function.identity;
import static java.util.stream.Collectors.toMap;
import static java.util.stream.Collectors.toSet;
import static org.springframework.data.mongodb.core.query.Criteria.where;
import static org.springframework.data.mongodb.core.query.Query.query;

import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.EntityReference;
import com.solum.xplain.core.common.csv.ImportOptions;
import com.solum.xplain.core.common.csv.NewVersionFormV2Utils;
import com.solum.xplain.core.common.value.ArchiveEntityForm;
import com.solum.xplain.core.common.value.DateList;
import com.solum.xplain.core.common.value.NewVersionFormV2;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.common.versions.GenericUniqueVersionedEntityRepository;
import com.solum.xplain.core.common.versions.VersionedEntity;
import com.solum.xplain.core.company.CompanySettingsType;
import com.solum.xplain.core.company.entity.CompanyLegalEntity;
import com.solum.xplain.core.company.entity.CompanyLegalEntityValuationSettings;
import com.solum.xplain.core.company.entity.CompanyValuationSettings;
import com.solum.xplain.core.company.entity.ValuationSettings;
import com.solum.xplain.core.company.entity.ValuationSettingsMarketDataGroup;
import com.solum.xplain.core.company.entity.ValuationSettingsNames;
import com.solum.xplain.core.company.entity.csv.CompanyLegalEntityCsvForm;
import com.solum.xplain.core.company.events.CompanyArchived;
import com.solum.xplain.core.company.events.CompanyLegalEntityArchived;
import com.solum.xplain.core.company.events.CompanyLegalEntityCreated;
import com.solum.xplain.core.company.events.CompanyLegalEntityImported;
import com.solum.xplain.core.company.form.ValuationSettingsForm;
import com.solum.xplain.core.company.mapper.LegalEntityValuationSettingsMapper;
import com.solum.xplain.core.company.value.CompanyLegalEntityValuationSettingsView;
import com.solum.xplain.core.curveconfiguration.event.CurveConfigurationNameChanged;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.market.events.MarketDataGroupUpdated;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Stream;
import org.jspecify.annotations.NullMarked;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.event.EventListener;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

@Repository
@NullMarked
public class CompanyLegalEntityValuationSettingsRepository
    extends GenericUniqueVersionedEntityRepository<CompanyLegalEntityValuationSettings> {
  private final MongoOperations mongoOperations;
  private final LegalEntityValuationSettingsMapper mapper;
  private final CompanyEntityValuationSettingsResolver valuationSettingsResolver;
  private final CompanyValuationSettingsRepository companyValuationSettingsRepository;

  public CompanyLegalEntityValuationSettingsRepository(
      MongoOperations mongoOperations,
      @Lazy CompanyValuationSettingsRepository companyValuationSettingsRepository,
      CompanyEntityValuationSettingsResolver valuationSettingsResolver) {
    super(mongoOperations, LegalEntityValuationSettingsMapper.INSTANCE);
    this.mongoOperations = mongoOperations;
    this.mapper = LegalEntityValuationSettingsMapper.INSTANCE;
    this.valuationSettingsResolver = valuationSettingsResolver;
    this.companyValuationSettingsRepository = companyValuationSettingsRepository;
  }

  @Override
  protected Criteria uniqueEntityCriteria(CompanyLegalEntityValuationSettings entity) {
    return hasEntityId(entity.getEntityId());
  }

  public Either<ErrorItem, EntityId> updateCompanyEntityValuationSettings(
      String entityId,
      LocalDate version,
      ValuationSettingsForm form,
      ValuationSettingsNames names) {
    return entityExact(entityId, version)
        .map(
            entity ->
                update(
                    entity,
                    form.getVersionForm(),
                    copiedEntity -> updateCompanyValuationSettings(form, names, copiedEntity)));
  }

  private CompanyLegalEntityValuationSettings updateCompanyValuationSettings(
      ValuationSettingsForm form,
      ValuationSettingsNames names,
      CompanyLegalEntityValuationSettings entity) {
    var marketDataGroup =
        Optional.ofNullable(form.getMarketDataGroupId())
            .map(
                v ->
                    ValuationSettingsMarketDataGroup.marketDataGroup(
                        v, names.getMarketDataGroupName()))
            .orElse(null);
    if (CompanySettingsType.valueOf(form.getSettingsType()) == DEFAULT) {
      return mapper.fromFormToDefault(form, marketDataGroup, entity);
    } else {
      return mapper.fromForm(form, names, marketDataGroup, entity);
    }
  }

  public List<CompanyLegalEntityValuationSettingsView> getCompanyValuationSettingsVersions(
      String entityId) {
    return entityVersions(entityId).stream().map(mapper::toView).toList();
  }

  public Stream<CompanyLegalEntityValuationSettingsView> getCompanyLegalEntitySettingsOfCompanies(
      Collection<String> allCompanyIds, BitemporalDate stateDate) {

    var rawEntityLevelSettings =
        entities(stateDate, active(), where(CompanyLegalEntity.Fields.companyId).in(allCompanyIds));

    var applicableCompanySettingsByCompanyId =
        companyValuationSettingsRepository
            .companySettingsEntities(allCompanyIds, stateDate)
            .stream()
            .collect(toMap(CompanyValuationSettings::getEntityId, identity()));

    return rawEntityLevelSettings.stream()
        .map(
            it -> {
              var companySettings = applicableCompanySettingsByCompanyId.get(it.getCompanyId());
              var legalEntitySettings = applicableLegalEntitySettings(it, companySettings);
              return mapper.toView(legalEntitySettings);
            });
  }

  public Map<String, CompanyLegalEntityValuationSettingsView> getCompanyLegalEntitySettings(
      Collection<String> entityIds, BitemporalDate stateDate) {

    var rawEntityLevelSettings =
        entities(stateDate, active(), where(VersionedEntity.Fields.entityId).in(entityIds));

    var allCompanyIds =
        rawEntityLevelSettings.stream()
            .map(CompanyLegalEntityValuationSettings::getCompanyId)
            .collect(toSet());

    var applicableCompanySettingsByCompanyId =
        companyValuationSettingsRepository
            .companySettingsEntities(allCompanyIds, stateDate)
            .stream()
            .collect(toMap(CompanyValuationSettings::getEntityId, identity()));

    return rawEntityLevelSettings.stream()
        .collect(
            toMap(
                CompanyLegalEntityValuationSettings::getEntityId,
                it -> {
                  var companySettings = applicableCompanySettingsByCompanyId.get(it.getCompanyId());
                  var legalEntitySettings = applicableLegalEntitySettings(it, companySettings);
                  return mapper.toView(legalEntitySettings);
                }));
  }

  public CompanyLegalEntityValuationSettings getCompanyEntitySettings(
      String companyId, String entityId, BitemporalDate stateDate) {
    var defaultSettings =
        companyValuationSettingsRepository.companySettingsEntity(companyId, stateDate);

    var entity =
        entity(entityId, stateDate, active())
            .getOr(() -> defaultSettings.toDefaultLegalEntitySettings(entityId));
    entity.applyDefaults(defaultSettings);
    return entity;
  }

  public CompanyLegalEntityValuationSettingsView getCompanyEntitySettingsView(
      String companyId, String entityId, BitemporalDate stateDate) {
    var entity = getCompanyEntitySettings(companyId, entityId, stateDate);
    return mapper.toView(entity);
  }

  @EventListener
  public void onMarketDataGroupUpdated(MarketDataGroupUpdated event) {
    mongoOperations.updateMulti(
        query(
            where(
                    propertyName(
                        ValuationSettings.Fields.marketDataGroup,
                        ValuationSettingsMarketDataGroup.Fields.marketDataGroupId))
                .is(event.getEntityId())),
        new Update()
            .set(
                propertyName(
                    ValuationSettings.Fields.marketDataGroup,
                    ValuationSettingsMarketDataGroup.Fields.marketDataGroupName),
                event.getForm().getName()),
        CompanyLegalEntityValuationSettings.class);
  }

  @EventListener
  public void onCurveConfigurationNameUpdated(CurveConfigurationNameChanged event) {
    mongoOperations.updateMulti(
        query(
            where(
                    propertyName(
                        ValuationSettings.Fields.curveConfiguration,
                        EntityReference.Fields.entityId))
                .is(event.getEntityId())),
        new Update()
            .set(
                propertyName(
                    ValuationSettings.Fields.curveConfiguration, EntityReference.Fields.name),
                event.getNewName()),
        CompanyLegalEntityValuationSettings.class);

    mongoOperations.updateMulti(
        query(
            where(
                    propertyName(
                        ValuationSettings.Fields.nonFxCurveConfiguration,
                        EntityReference.Fields.entityId))
                .is(event.getEntityId())),
        new Update()
            .set(
                propertyName(
                    ValuationSettings.Fields.nonFxCurveConfiguration, EntityReference.Fields.name),
                event.getNewName()),
        CompanyLegalEntityValuationSettings.class);
  }

  @EventListener
  public void onCompanyArchived(CompanyArchived event) {
    entities(
            ROOT_DATE,
            where(CompanyLegalEntityValuationSettings.Fields.companyId).is(event.getEntityId()))
        .forEach(this::forceArchive);
  }

  @EventListener
  public void onCompanyEntityCreated(CompanyLegalEntityCreated event) {
    if (event instanceof CompanyLegalEntityImported importedEvent) {
      onCompanyEntityCreatedImport(importedEvent);
    } else {
      insert(
          CompanyLegalEntityValuationSettings.newOf(event.getCompanyId(), event.getEntityId()),
          NewVersionFormV2.newDefault());
    }
  }

  private void onCompanyEntityCreatedImport(CompanyLegalEntityImported event) {
    var importCurveConfig = event.getCurveConfiguration();
    var importMdg = event.getMarketDataGroup();
    var importCompanyId = event.getCompanyId();
    var importEntityId = event.getEntityId();
    var existingCompanyEntitySettings =
        getCompanyEntitySettings(importCompanyId, importEntityId, event.getStateDate());

    var valuationSettings =
        CompanyLegalEntityValuationSettings.newOf(importCompanyId, importEntityId);

    if (importCurveConfig != null || importMdg != null) {
      valuationSettings.applyDefaults(existingCompanyEntitySettings);
      valuationSettings.setSettingsType(BESPOKE);
    }

    if (importCurveConfig != null) {
      valuationSettings.setCurveConfiguration(importCurveConfig);
    }

    if (importMdg != null) {
      valuationSettings.setMarketDataGroup(
          ValuationSettingsMarketDataGroup.marketDataGroup(
              importMdg.getEntityId(), importMdg.getName()));
    }

    insert(valuationSettings, NewVersionFormV2.newDefault());
  }

  @EventListener
  public void onCompanyEntityArchived(CompanyLegalEntityArchived event) {
    entityExact(event.getEntityId(), ROOT_DATE).forEach(this::forceArchive);
  }

  public EntityId updateValuationSettings(
      String companyId,
      String entityId,
      CompanyLegalEntityCsvForm entityCsvForm,
      ImportOptions importOptions) {

    var version = importOptions.bitemporalDate();
    var currEntitySettings =
        entity(entityId, version, active())
            .getOr(() -> CompanyLegalEntityValuationSettings.newOf(companyId, entityId));
    var currEntitySettingsWithDefaults = getCompanyEntitySettings(companyId, entityId, version);

    var versionForm =
        NewVersionFormV2Utils.fromImportOptions(
            importOptions,
            currEntitySettingsWithDefaults.getValidFrom(),
            importOptions::getFutureVersionsAction);

    return updateValuationSettings(
        currEntitySettings, currEntitySettingsWithDefaults, entityCsvForm, versionForm);
  }

  private EntityId updateValuationSettings(
      CompanyLegalEntityValuationSettings currEntitySettings,
      CompanyLegalEntityValuationSettings currEntitySettingsWithDefaults,
      CompanyLegalEntityCsvForm companyCsvForm,
      NewVersionFormV2 vf) {

    var valuationSettingsImportData =
        new ValuationSettingsImportUpdateData(
            companyCsvForm.getCurveConfiguration(), companyCsvForm.getMarketDataGroup(), vf);

    // compare currEntitySettings before and after entity settings are applied
    return update(
        currEntitySettings,
        vf,
        v ->
            valuationSettingsResolver.updateValuationSettings(
                v, currEntitySettingsWithDefaults, valuationSettingsImportData));
  }

  public DateList getFutureVersions(String entityId, LocalDate stateDate) {
    var searchCriteria = hasEntityId(entityId);
    return futureVersionsByCriteria(searchCriteria, stateDate);
  }

  public List<CompanyLegalEntityValuationSettings> getValuationSettings(
      BitemporalDate stateDate, String companyId) {
    return entities(
        stateDate,
        active(),
        where(CompanyLegalEntityValuationSettings.Fields.companyId).is(companyId));
  }

  private void forceArchive(CompanyLegalEntityValuationSettings e) {
    archive(
        e,
        new ArchiveEntityForm(
            NewVersionFormV2.builder()
                .comment("Entity archived")
                .validFrom(ROOT_DATE)
                .futureVersionsAction(DELETE)
                .build()));
  }

  public Either<ErrorItem, EntityId> deleteValuationSettings(String entityId, LocalDate version) {
    return entityExact(entityId, version).flatMap(this::delete);
  }
}
