package com.solum.xplain.core.curvegroup.curve.entity;

import static com.solum.xplain.core.classifiers.CurveNodeTypes.FRA_NODE;
import static com.solum.xplain.core.classifiers.CurveNodeTypes.IBOR_FUTURE_NODE;
import static com.solum.xplain.core.classifiers.CurveNodeTypes.IMM_FRA_NODE;
import static com.solum.xplain.core.utils.TenorUtils.sumOfMonths;
import static java.lang.Integer.parseInt;

import com.opengamma.strata.basics.date.Tenor;
import com.solum.xplain.core.classifiers.Constants;
import com.solum.xplain.core.curvegroup.ParsableTenor;
import com.solum.xplain.core.utils.TenorUtils;
import java.time.Period;
import org.apache.commons.lang3.StringUtils;
import org.jspecify.annotations.NullMarked;

@NullMarked
public interface CurveNodeInstrument extends ParsableTenor {

  String getConvention();

  String getSerialFuture();

  String getFraSettlement();

  String getType();

  String getPeriod();

  default String getInstrument() {
    if (FRA_NODE.equals(getType())) {
      var sumOfMonths = fraSumOfMonths();
      return getFraSettlement() + "x" + sumOfMonths; // e.g. 4Mx10M
    } else if (IBOR_FUTURE_NODE.equals(getType()) || IMM_FRA_NODE.equals(getType())) {
      return getSerialFuture();
    } else {
      return getPeriod();
    }
  }

  private String fraSumOfMonths() {
    var fraPeriod = TenorUtils.fromForwardRateAgreement(getConvention());
    return sumOfMonths(fraPeriod.toString(), getFraSettlement()).map(Tenor::toString).orElse("");
  }

  @Override
  default String getTenor() {
    if (FRA_NODE.equals(getType())) {
      return fraSumOfMonths();
    } else if (IBOR_FUTURE_NODE.equals(getType()) || IMM_FRA_NODE.equals(getType())) {
      // Not elegant. This is to allow RequireTnNodeValidator to proceed when serial future is not
      // provided so that we get the right error message
      if (!Constants.SERIAL_FUTURE_VALUES.contains(getSerialFuture())) {
        return "";
      }
      var futureNo = parseInt(StringUtils.substringAfter(getSerialFuture(), "+"));
      // Even 6 month conventions work on 3 month futures
      var period = Period.ofMonths(3).multipliedBy(futureNo + 1);
      return Tenor.ofMonths((int) period.toTotalMonths()).toString();
    }
    return getPeriod();
  }
}
