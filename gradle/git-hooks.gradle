
tasks.register('installGitHook', Copy) {
  from new File(projectDir, 'scripts/pre-push')
  into { new File(projectDir, '.git/hooks') }
  fileMode 0777
}

tasks.register('installPreCommitHook', Copy) {
  from new File(projectDir, 'scripts/pre-commit')
  into { new File(projectDir, '.git/hooks') }
  fileMode 0777
}

tasks.named('prepareKotlinBuildScriptModel').configure {
  dependsOn 'installGitHook', 'installPreCommitHook'
}