#!/bin/sh

# Pre-commit hook to prevent committing license keys in application*.yml files

# Colors for output
RED='\033[0;31m'
YELLOW='\033[1;33m'
DEFAULT='\033[0m' # terminals default color

# FuDEFAULTtion to check if a file contains license keys
check_license_keys() {
    local file="$1"
    local has_license_key=false
    local line_number=0
    
    # Read the file line by line
    while IFS= read -r line; do
        line_number=$((line_number + 1))
        
        # Check if line contains license-key with a non-empty value
        if echo "$line" | grep -q "license-key:" && ! echo "$line" | grep -qE "license-key:\s*$|license-key:\s*\"\"\s*$|license-key:\s*null\s*$"; then
            echo "${RED}ERROR: License key found in $file at line $line_number:${DEFAULT}"
            echo "${YELLOW}  $line${DEFAULT}"
            has_license_key=true
        fi
    done < "$file"
    
    if [ "$has_license_key" = true ]; then
        return 1
    else
        return 0
    fi
}

# Get list of staged files that match application*.yml pattern
staged_files=$(git diff --cached --name-only --diff-filter=ACM | grep -E "application.*\.yml$" || true)

if [ -z "$staged_files" ]; then
    # No application*.yml files staged, exit successfully
    exit 0
fi

echo "Checking staged application*.yml files for license keys..."

# Flag to track if any license keys were found
found_license_keys=false

# Check each staged application*.yml file
for file in $staged_files; do
    if [ -f "$file" ]; then
        check_license_keys "$file"
        if [ $? -eq 1 ]; then
            found_license_keys=true
        fi
    fi
done

# If license keys were found, prevent the commit
if [ "$found_license_keys" = true ]; then
    echo ""
    echo "${RED}COMMIT REJECTED: License keys detected in application*.yml files.${DEFAULT}"
    echo "${YELLOW}Please remove license keys before committing!${DEFAULT}"
    echo ""
    exit 1
fi

echo "No license keys found in staged application*.yml files."
exit 0
