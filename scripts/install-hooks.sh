#!/bin/bash

# Script to install git hooks for the project

echo "Installing git hooks..."

# Create .git/hooks directory if it doesn't exist
mkdir -p .git/hooks

# Copy pre-commit hook
cp scripts/pre-commit .git/hooks/pre-commit
chmod +x .git/hooks/pre-commit

# Copy pre-push hook if it exists
if [ -f scripts/pre-push ]; then
    cp scripts/pre-push .git/hooks/pre-push
    chmod +x .git/hooks/pre-push
fi

echo "✓ Git hooks installed successfully!"
echo "The pre-commit hook will now prevent commits with license keys in application*.yml files."
