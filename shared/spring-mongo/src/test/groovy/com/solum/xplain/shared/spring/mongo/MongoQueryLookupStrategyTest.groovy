package com.solum.xplain.shared.spring.mongo

import java.lang.reflect.Method
import org.springframework.data.mapping.context.MappingContext
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.aggregation.AggregationOperation
import org.springframework.data.mongodb.core.convert.MongoConverter
import org.springframework.data.mongodb.core.mapping.MongoPersistentEntity
import org.springframework.data.mongodb.repository.Aggregation
import org.springframework.data.mongodb.repository.Query
import org.springframework.data.mongodb.repository.query.PartTreeMongoQuery
import org.springframework.data.mongodb.repository.query.StringBasedAggregation
import org.springframework.data.mongodb.repository.query.StringBasedMongoQuery
import org.springframework.data.projection.ProjectionFactory
import org.springframework.data.repository.core.NamedQueries
import org.springframework.data.repository.core.RepositoryMetadata
import org.springframework.data.repository.query.ValueExpressionDelegate
import org.springframework.data.util.TypeInformation
import spock.lang.Specification

class MongoQueryLookupStrategyTest extends Specification {
  def mongoOperations = Mock(MongoOperations, {
    getConverter() >> Mock(MongoConverter, {
      getMappingContext() >> Mock(MappingContext)
    })
  })

  def valueExpressionDelegate = Mock(ValueExpressionDelegate)
  def mappingContext = Mock(MappingContext, {
    getPersistentEntity(TestEntity) >> Mock(MongoPersistentEntity, {
      getType() >> TestEntity
    })
  })
  def mongoQueryLookupStrategy = new MongoQueryLookupStrategy(mongoOperations, valueExpressionDelegate, mappingContext)
  def metadata = Mock(RepositoryMetadata, {
    getDomainType() >> TestEntity
    getDomainTypeInformation() >> Mock(TypeInformation, {
      getType() >> TestEntity
      getProperty(_ as String) >> Mock(TypeInformation, {
        getType() >> String
      })
    })
    getReturnType(_ as Method) >> Mock(TypeInformation, {
      getType() >> TestEntity
    })
    getReturnedDomainClass(_ as Method) >> TestEntity
  })
  def factory = Mock(ProjectionFactory)
  def namedQueries = Mock(NamedQueries, {
    hasQuery("TestEntity.findByNamedQuery") >> true
    getQuery("TestEntity.findByNamedQuery") >> "{ name: ?0 }"
  })

  static class TestEntity {
    String name
    String namedQuery
  }

  static class TestRepository {
    TestEntity findByName(String param) {}

    @Query("{ name: ?0 }")
    TestEntity findByQuery(String param) {}

    TestEntity findByNamedQuery(String param) {}

    @Aggregation(
    pipeline = ["{ \$match: { name: ?0 } }",]
    )
    TestEntity findByAggregation(String param) {}

    @Aggregation(
    pipeline = ["{ \$match: { name: ?0 } }",]
    )
    @AggregationParameters(TestAggregationParameterResolver)
    TestEntity findByAggregationWithParams(String name) {}
  }

  @AggregationParameters(TestAggregationParameterResolver)
  static class TestRepositoryWithAggregationParameters extends TestRepository {
  }

  static class TestAggregationParameterResolver implements AggregationParameterResolver {
    @Override
    List<AggregationOperation> getParameterOperations(Object parameter, Class<?> typeToRead, Class<?> entityClass) {
      return null
    }
  }

  def "should return standard Mongo repository queries"() {
    given:
    _ * metadata.getRepositoryInterface() >> TestRepository

    when:
    def q = mongoQueryLookupStrategy.resolveQuery(TestRepository.class.getMethod(methodName, String), metadata, factory, namedQueries)

    then:
    q.getClass() == queryImplementation

    where:
    methodName          | queryImplementation
    "findByName"        | PartTreeMongoQuery
    "findByQuery"       | StringBasedMongoQuery
    "findByNamedQuery"  | StringBasedMongoQuery
    "findByAggregation" | StringBasedAggregation
  }

  def "should return extensible repository queries"() {
    given:
    _ * metadata.getRepositoryInterface() >> TestRepositoryWithAggregationParameters

    when:
    def q = mongoQueryLookupStrategy.resolveQuery(TestRepositoryWithAggregationParameters.class.getMethod(methodName, String), metadata, factory, namedQueries)

    then:
    q.getClass() == queryImplementation
    if (supportsParams) {
      assert q.aggregationParameterResolver instanceof TestAggregationParameterResolver
    }

    where:
    methodName                    | queryImplementation              | supportsParams
    "findByName"                  | PartTreeMongoAggregation         | true
    "findByQuery"                 | StringBasedMongoQuery            | false
    "findByNamedQuery"            | StringBasedMongoQuery            | false
    "findByAggregation"           | ExtensibleStringBasedAggregation | true
    "findByAggregationWithParams" | ExtensibleStringBasedAggregation | true
  }

  def "should support method-level annotation"() {
    given:
    _ * metadata.getRepositoryInterface() >> TestRepository

    when:
    def q = mongoQueryLookupStrategy.resolveQuery(TestRepository.class.getMethod("findByAggregationWithParams", String), metadata, factory, namedQueries)

    then:
    q.getClass() == ExtensibleStringBasedAggregation
    q.aggregationParameterResolver instanceof TestAggregationParameterResolver
  }
}
