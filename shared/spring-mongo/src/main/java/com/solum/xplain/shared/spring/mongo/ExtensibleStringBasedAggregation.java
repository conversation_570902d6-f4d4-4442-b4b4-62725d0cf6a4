package com.solum.xplain.shared.spring.mongo;

import lombok.Getter;
import lombok.SneakyThrows;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.aggregation.AggregationPipeline;
import org.springframework.data.mongodb.repository.query.ConvertingParameterAccessor;
import org.springframework.data.mongodb.repository.query.MongoQueryMethod;
import org.springframework.data.mongodb.repository.query.StringBasedAggregation;
import org.springframework.data.repository.query.ResultProcessor;
import org.springframework.data.repository.query.ValueExpressionDelegate;
import org.springframework.lang.Nullable;
import org.springframework.util.Assert;

/**
 * Class derived from Spring's {@link StringBasedAggregation} which allows for extending the
 * aggregation pipeline with custom stages based on an {@link AggregationParameterResolver}.
 */
@Getter
public class ExtensibleStringBasedAggregation extends StringBasedAggregation
    implements ExtensibleAggregation {
  private final MongoOperations mongoOperations;
  private final AggregationParameterResolver aggregationParameterResolver;

  public ExtensibleStringBasedAggregation(
      MongoQueryMethod method,
      MongoOperations mongoOperations,
      ValueExpressionDelegate delegate,
      AggregationParameterResolver aggregationParameterResolver) {
    super(method, mongoOperations, delegate);
    this.mongoOperations = mongoOperations;
    this.aggregationParameterResolver = aggregationParameterResolver;
  }

  @Override
  @SneakyThrows
  @Nullable
  protected Object doExecute(
      MongoQueryMethod method,
      ResultProcessor processor,
      ConvertingParameterAccessor accessor,
      @Nullable Class<?> typeToRead) {
    Assert.notNull(typeToRead, "typeToRead must not be null for aggregation");

    var aggregationOperations =
        getDetectedFieldAggregationOperations(
            accessor, typeToRead, method.getEntityInformation().getCollectionEntity().getType());
    if (aggregationOperations.isEmpty() && !hasSortAggregationOperations(accessor)) {
      return super.doExecute(method, processor, accessor, typeToRead);
    }

    AggregationPipeline pipeline =
        new AggregationPipeline(
            parseAggregationPipeline(method.getAnnotatedAggregation(), accessor));
    aggregationOperations.forEach(pipeline::add);

    return executePipelineAsAggregation(method, accessor, typeToRead, pipeline, false, false);
  }
}
