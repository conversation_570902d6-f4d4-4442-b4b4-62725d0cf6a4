package com.solum.xplain.shared.spring.mongo;

import java.util.List;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;

public interface AggregationParameterResolver {

  /**
   * Provides a list of zero or more {@link AggregationOperation} to be added to the aggregation
   * pipeline that implement the required pipeline stages for the supplied parameter.
   *
   * @param parameter the parameter to resolve
   * @param typeToRead the type of the result of the aggregation, i.e. the return type of the query
   *     method.
   * @param entityClass the type of the entity being aggregated, i.e. the domain type of the
   *     repository.
   */
  List<AggregationOperation> getParameterOperations(
      Object parameter, Class<?> typeToRead, Class<?> entityClass);
}
