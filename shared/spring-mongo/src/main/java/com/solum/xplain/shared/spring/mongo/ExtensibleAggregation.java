package com.solum.xplain.shared.spring.mongo;

import static com.solum.xplain.shared.spring.mongo.AggregationUtils.applyHint;
import static com.solum.xplain.shared.spring.mongo.AggregationUtils.applyMeta;
import static com.solum.xplain.shared.spring.mongo.AggregationUtils.applyReadPreference;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;
import java.util.function.LongUnaryOperator;
import java.util.stream.Stream;
import lombok.SneakyThrows;
import org.bson.Document;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.SliceImpl;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.AggregationOptions;
import org.springframework.data.mongodb.core.aggregation.AggregationPipeline;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.aggregation.TypedAggregation;
import org.springframework.data.mongodb.core.convert.MongoConverter;
import org.springframework.data.mongodb.core.mapping.MongoSimpleTypes;
import org.springframework.data.mongodb.core.query.Collation;
import org.springframework.data.mongodb.repository.query.ConvertingParameterAccessor;
import org.springframework.data.mongodb.repository.query.MongoQueryMethod;
import org.springframework.data.repository.query.ResultProcessor;
import org.springframework.data.util.ReflectionUtils;
import org.springframework.data.util.TypeInformation;
import org.springframework.lang.Nullable;
import org.springframework.util.Assert;
import org.springframework.util.ClassUtils;

/**
 * Mixin to provide common functionality for aggregations which need to add additional operations
 * using an {@link AggregationParameterResolver} such as {@link ExtensibleStringBasedAggregation}
 * and {@link PartTreeMongoAggregation}.
 */
public interface ExtensibleAggregation {
  /** Hide private methods from the public API. */
  class Internal {
    private static final String COUNT_FIELD = "count";

    private static boolean isSimpleType(Class<?> targetType) {
      return MongoSimpleTypes.HOLDER.isSimpleType(targetType);
    }

    private static Class<?> getDomainClass(MongoQueryMethod method)
        throws NoSuchMethodException, IllegalAccessException, InvocationTargetException {
      // Use reflection because getDomainClass is not public
      Method toInvoke = method.getClass().getDeclaredMethod("getDomainClass");
      toInvoke.setAccessible(true);
      return (Class<?>) toInvoke.invoke(method);
    }

    private static TypeInformation<?> getReturnType(MongoQueryMethod method)
        throws NoSuchMethodException, IllegalAccessException, InvocationTargetException {
      // Use reflection because getReturnType is not public
      Method toInvoke = method.getClass().getDeclaredMethod("getReturnType");
      toInvoke.setAccessible(true);
      return (TypeInformation<?>) toInvoke.invoke(method);
    }

    @SneakyThrows
    private static AggregationOptions computeOptions(
        MongoQueryMethod method,
        ConvertingParameterAccessor accessor,
        AggregationPipeline pipeline) {

      AggregationOptions.Builder builder = Aggregation.newAggregationOptions();

      var annotatedCollation =
          method.hasAnnotatedCollation() ? method.getAnnotatedCollation() : null;
      applyCollation(builder, annotatedCollation, accessor);
      applyMeta(builder, method);
      applyHint(builder, method);
      applyReadPreference(builder, method);

      if (ReflectionUtils.isVoid(getReturnType(method).getType()) && pipeline.isOutOrMerge()) {
        builder.skipOutput();
      }

      return builder.build();
    }

    private static void applyCollation(
        AggregationOptions.Builder builder,
        @Nullable String collationExpression,
        ConvertingParameterAccessor accessor) {

      Assert.state(
          collationExpression == null, "@Collation annotation not supported on aggregation");

      Collation collation = accessor.getCollation();
      if (collation != null) {
        builder.collation(collation);
      }
    }

    private static List<Object> convertResults(
        Class<?> typeToRead, List<Object> mappedResults, MongoConverter mongoConverter) {

      List<Object> list = new ArrayList<>(mappedResults.size());
      for (Object it : mappedResults) {
        Object extractSimpleTypeResult =
            AggregationUtils.extractSimpleTypeResult((Document) it, typeToRead, mongoConverter);
        list.add(extractSimpleTypeResult);
      }
      return list;
    }
  }

  AggregationParameterResolver getAggregationParameterResolver();

  MongoOperations getMongoOperations();

  default MongoConverter getMongoConverter() {
    return getMongoOperations().getConverter();
  }

  default List<AggregationOperation> getDetectedFieldAggregationOperations(
      ConvertingParameterAccessor accessor, Class<?> typeToRead, Class<?> entityClass) {
    List<AggregationOperation> operations = new ArrayList<>();
    for (Object parameter : accessor.getValues()) {
      operations.addAll(
          getAggregationParameterResolver()
              .getParameterOperations(parameter, typeToRead, entityClass));
    }
    return operations;
  }

  default boolean hasSortAggregationOperations(ConvertingParameterAccessor accessor) {
    return accessor.getSort() instanceof PreSortOperations
        || accessor.getSort() instanceof PostSortOperations;
  }

  /**
   * This method is derived from {@link
   * org.springframework.data.mongodb.repository.query.StringBasedAggregation#doExecute(MongoQueryMethod,
   * ResultProcessor, ConvertingParameterAccessor, Class)} and {@link
   * org.springframework.data.mongodb.repository.query.AggregationUtils#doAggregate(AggregationPipeline,
   * MongoQueryMethod, ResultProcessor, ConvertingParameterAccessor, Function,
   * org.springframework.data.mongodb.repository.query.AggregationUtils.AggregationCallback)}
   */
  @Nullable
  default <T> Object executePipelineAsAggregation(
      MongoQueryMethod method,
      ConvertingParameterAccessor accessor,
      Class<T> typeToRead,
      AggregationPipeline pipeline,
      boolean isExistsQuery,
      boolean isCountQuery)
      throws NoSuchMethodException, IllegalAccessException, InvocationTargetException {
    var mongoOperations = getMongoOperations();
    var mongoConverter = getMongoConverter();

    // This first bit is from AggregationUtils.doAggregate()
    var sourceType = Internal.getDomainClass(method);

    Class<?> targetType = typeToRead;

    AggregationUtils.appendSortIfPresent(pipeline, accessor, typeToRead);

    if (method.isSliceQuery()) {
      AggregationUtils.appendLimitAndOffsetIfPresent(
          pipeline, accessor, LongUnaryOperator.identity(), limit -> limit + 1);
    } else {
      AggregationUtils.appendLimitAndOffsetIfPresent(pipeline, accessor);
    }

    boolean isSimpleReturnType = Internal.isSimpleType(typeToRead);
    boolean isRawAggregationResult = ClassUtils.isAssignable(AggregationResults.class, typeToRead);

    if (isSimpleReturnType) {
      targetType = Document.class;
    } else if (isRawAggregationResult) {

      // 🙈
      targetType =
          Internal.getReturnType(method)
              .getRequiredActualType()
              .getRequiredComponentType()
              .getType();
    }

    AggregationOptions options = Internal.computeOptions(method, accessor, pipeline);
    TypedAggregation<?> aggregation =
        new TypedAggregation<>(sourceType, pipeline.getOperations(), options);

    // This next bit is from the callback defined in StringBasedAggregation
    if (method.isStreamQuery()) {

      Stream<?> stream = mongoOperations.aggregateStream(aggregation, targetType);

      if (isSimpleReturnType) {
        return stream.map(
            it ->
                AggregationUtils.extractSimpleTypeResult(
                    (Document) it, typeToRead, mongoConverter));
      }

      return stream;
    }

    if (isExistsQuery) {
      aggregation.getPipeline().add(Aggregation.limit(1));
    }
    if (isCountQuery || isExistsQuery) { // for exists count is 0 or 1
      aggregation.getPipeline().add(Aggregation.count().as(Internal.COUNT_FIELD));
    }

    @SuppressWarnings("unchecked")
    AggregationResults<Object> result =
        (AggregationResults<Object>) mongoOperations.aggregate(aggregation, targetType);
    if (ReflectionUtils.isVoid(typeToRead)) {
      return null;
    }

    if (isRawAggregationResult) {
      return result;
    }

    List<Object> results = result.getMappedResults();
    if (method.isCollectionQuery()) {
      return isSimpleReturnType
          ? Internal.convertResults(typeToRead, results, mongoConverter)
          : results;
    }

    if (method.isSliceQuery()) {

      Pageable pageable = accessor.getPageable();
      List<Object> resultsToUse =
          isSimpleReturnType
              ? Internal.convertResults(typeToRead, results, mongoConverter)
              : results;
      int pageSize = pageable.isUnpaged() ? resultsToUse.size() : pageable.getPageSize();
      boolean hasNext = resultsToUse.size() > pageSize;
      return new SliceImpl<>(
          hasNext ? resultsToUse.subList(0, pageSize) : resultsToUse, pageable, hasNext);
    }

    Object uniqueResult = result.getUniqueMappedResult();

    if (isExistsQuery) {
      return uniqueResult != null && ((Document) uniqueResult).getInteger(Internal.COUNT_FIELD) > 0;
    }

    return isSimpleReturnType
        ? AggregationUtils.extractSimpleTypeResult(
            (Document) uniqueResult, typeToRead, mongoConverter)
        : uniqueResult;
  }
}
