package com.solum.xplain.shared.spring.mongo

import org.bson.types.ObjectId
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.autoconfigure.domain.EntityScan
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.data.annotation.Id
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.aggregation.AddFieldsOperation
import org.springframework.data.mongodb.core.aggregation.Aggregation
import org.springframework.data.mongodb.core.aggregation.AggregationOperation
import org.springframework.data.mongodb.core.aggregation.ArithmeticOperators
import org.springframework.data.mongodb.core.mapping.Document
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.data.mongodb.repository.MongoRepository
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories
import org.springframework.data.util.Streamable
import org.springframework.lang.Nullable

@SpringBootTest
class ExtensibleAggregationIntegrationTest extends IntegrationSpecification {
  @Autowired
  MongoOperations mongoOperations
  @Autowired
  TestRepository testRepository

  @TestConfiguration
  @EnableMongoRepositories(basePackageClasses = TestRepository, repositoryFactoryBeanClass = XplainMongoRepositoryFactoryBean.class)
  @EntityScan(basePackageClasses = TestEntity)
  static class TestConfig {
  }

  def "should allow for extensions using PartTreeMongoAggregation"() {
    given:
    mongoOperations.insertAll([
      new TestEntity(name: "test1", thisOne: false, a: 1, b: 10),
      new TestEntity(name: "test1", thisOne: true, a: 2, b: 4),
    ])

    when:
    def results = testRepository.findByName("test1", filter, sort)

    then:
    results*.a == expected

    cleanup:
    mongoOperations.remove(new Query(), TestEntity)

    where:
    filter                         | sort                                   | expected
    null                           | Sort.by("a")                           | [1, 2]
    null                           | new EnhancedSort([Sort.Order.by("c")]) | [2, 1]
    new Filter(onlyThisOne: false) | Sort.by("b")                           | [2, 1]
    new Filter(onlyThisOne: true)  | Sort.unsorted()                        | [2]
  }

  def "should allow for extensions using ExtensibleStringMongoAggregation"() {
    given:
    mongoOperations.insertAll([
      new TestEntity(name: "test1", thisOne: false, a: 1, b: 10),
      new TestEntity(name: "test1", thisOne: true, a: 2, b: 4),
    ])

    when:
    def results = testRepository.customAggregation("test1", filter, sort)

    then:
    results*.a == expected
    results[0].name == "TEST1"

    cleanup:
    mongoOperations.remove(new Query(), TestEntity)

    where:
    filter                         | sort                                   | expected
    null                           | Sort.by("a")                           | [1, 2]
    null                           | new EnhancedSort([Sort.Order.by("c")]) | [2, 1]
    new Filter(onlyThisOne: false) | Sort.by("b")                           | [2, 1]
    new Filter(onlyThisOne: true)  | Sort.unsorted()                        | [2]
  }
}

@Document
class TestEntity {
  @Id
  ObjectId id

  String name
  int a
  int b
  boolean thisOne
}

@AggregationParameters(ThisOneCondition)
interface TestRepository extends MongoRepository<TestEntity, ObjectId> {
  List<TestEntity> findByName(String name, @Nullable Filter filter, @Nullable Sort sort)

  @org.springframework.data.mongodb.repository.Aggregation(pipeline = ["{ \$match: { name: ?0 } }", "{ \$addFields: { name: { \$toUpper: '\$name' } } }"])
  List<TestEntity> customAggregation(String name, @Nullable Filter filter, @Nullable Sort sort)
}

class Filter {
  boolean onlyThisOne
}

class EnhancedSort extends Sort implements PreSortOperations, PostSortOperations {
  EnhancedSort(List<Order> orders) {
    super(orders)
  }

  @Override
  Streamable<AggregationOperation> preSortOperations() {
    Streamable.of(AddFieldsOperation.addField("c")
      .withValueOf(ArithmeticOperators.Multiply.valueOf("a").multiplyBy("b")).build())
  }

  @Override
  Streamable<AggregationOperation> postSortOperations() {
    Streamable.of(AddFieldsOperation.addField("c").withValue(Aggregation.REMOVE).build())
  }
}

class ThisOneCondition implements AggregationParameterResolver {
  @Override
  List<AggregationOperation> getParameterOperations(Object parameter, Class<?> typeToRead, Class<?> entityClass) {
    if (parameter instanceof Filter) {
      if (parameter.onlyThisOne) {
        return [Aggregation.match(Criteria.where("thisOne").is(true))]
      }
    }
    return []
  }
}
