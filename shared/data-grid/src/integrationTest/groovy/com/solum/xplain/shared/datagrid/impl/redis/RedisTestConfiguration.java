package com.solum.xplain.shared.datagrid.impl.redis;

import com.solum.xplain.shared.datagrid.DataGridAutoConfiguration;
import com.solum.xplain.shared.datagrid.event.ClusterEventConfig;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;

@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@ActiveProfiles("test")
@ContextConfiguration(
    classes = {
      ClusterEventConfig.class,
      DataGridAutoConfiguration.class,
      RedisAutoConfiguration.class,
      TaskExecutionAutoConfiguration.class
    })
@TestPropertySource(
    properties = {
      "debug=true",
      "app.cluster.event-topic=test-topic",
      "app.data-grid.hazelcast.enabled=false",
      "app.data-grid.redis.enabled=true",
      "spring.data.redis.client-name=test"
    })
public @interface RedisTestConfiguration {}
