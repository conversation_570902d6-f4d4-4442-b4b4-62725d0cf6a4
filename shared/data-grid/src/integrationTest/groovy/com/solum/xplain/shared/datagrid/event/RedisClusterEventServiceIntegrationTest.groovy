package com.solum.xplain.shared.datagrid.event

import com.solum.xplain.core.helper.RedisIntegrationSpecification
import com.solum.xplain.shared.datagrid.ClusterEventPublisher
import com.solum.xplain.shared.datagrid.DataGridAutoConfiguration
import java.util.concurrent.CompletableFuture
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration
import org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.event.EventListener
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.context.TestPropertySource

@SpringBootTest
@ActiveProfiles("test")
@ContextConfiguration(classes = [ClusterEventConfig, TestConfig, DataGridAutoConfiguration, RedisAutoConfiguration, TaskExecutionAutoConfiguration])
@TestPropertySource(properties = [
  "debug=true",
  "app.cluster.event-topic=test-topic",
  "app.data-grid.hazelcast.enabled=false",
  "app.data-grid.redis.enabled=true",
  "spring.data.redis.client-name=test",
])
@EnableConfigurationProperties([ClusterProperties])
class RedisClusterEventServiceIntegrationTest extends RedisIntegrationSpecification {
  @Autowired
  ClusterEventPublisher clusterEventPublisher
  @Autowired
  TestEventReceiver testEventReceiver

  def "cluster event publisher should result in application event being received"() {
    when:
    clusterEventPublisher.publishEvent(new TestEvent(value: "success"))
    def receivedValue = testEventReceiver.waitForReceipt()

    then:
    receivedValue == "success"
  }

  @TestConfiguration
  static class TestConfig {
    @Bean
    TestEventReceiver receiver() {
      return new TestEventReceiver()
    }
  }

  static class TestEvent implements Serializable {
    String value
  }

  static class TestEventReceiver {
    CompletableFuture<String> receivedValue = new CompletableFuture<>()

    @EventListener
    def onEvent(TestEvent event) {
      receivedValue.complete(event.value)
    }

    String waitForReceipt() {
      return receivedValue.get()
    }
  }
}
