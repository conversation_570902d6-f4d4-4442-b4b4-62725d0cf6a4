package com.solum.xplain.shared.datagrid.impl.redis

import com.redis.testcontainers.RedisContainer
import com.solum.xplain.core.helper.RedisIntegrationSpecification
import com.solum.xplain.shared.datagrid.DataGridAutoConfiguration
import com.solum.xplain.shared.datagrid.FencingTokenService
import com.solum.xplain.shared.datagrid.event.ClusterEventConfig
import com.solum.xplain.shared.datagrid.event.ClusterProperties
import java.util.concurrent.*
import java.util.concurrent.atomic.AtomicInteger
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.redis.connection.RedisStandaloneConfiguration
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.context.TestPropertySource
import spock.lang.Shared
import spock.lang.Timeout
import spock.lang.Unroll

@SpringBootTest
@RedisTestConfiguration
@EnableConfigurationProperties([ClusterProperties])
class RedisLockManagerIntegrationTest extends RedisIntegrationSpecification {

  @Shared
  static RedisContainer redis1 = new RedisContainer("redis:7.4")
  @Shared
  static RedisContainer redis2 = new RedisContainer("redis:7.4")
  @Shared
  static RedisContainer redis3 = new RedisContainer("redis:7.4")

  @Shared
  List<JedisConnectionFactory> connectionFactories = []

  @Autowired
  RedisLockManager redisLockManager

  @Autowired
  FencingTokenService fencingTokenService

  def setupSpec() {
    [redis1, redis2, redis3]*.start()
    connectionFactories.addAll(createConnectionFactories([redis1, redis2, redis3]))
  }

  def cleanupSpec() {
    connectionFactories?.each { factory ->
      try {
        factory.destroy()
      } catch (Exception ignored) {}
    }
    [redis1, redis2, redis3]*.stop()
  }

  private static List<JedisConnectionFactory> createConnectionFactories(List<RedisContainer> containers) {
    containers.collect { container ->
      def config = new RedisStandaloneConfiguration(container.host, container.firstMappedPort)
      def factory = new JedisConnectionFactory(config)
      factory.afterPropertiesSet()
      return factory
    }
  }

  def cleanup() {
    // Clean up any remaining locks
    def activeKeys = new ArrayList<>(redisLockManager.heartbeatTasksByLockId.keySet())
    activeKeys.each { key ->
      try {
        def task = redisLockManager.heartbeatTasksByLockId.get(key)
        if (task) {
          task.lock.unlockAll()
          redisLockManager.release(task.lock)
        }
      } catch (Exception ignored) {
      }
    }
  }

  def "should successfully get a lock with heartbeat"() {
    given:
    def lockKey = "basic-test-" + UUID.randomUUID()
    def redisLock = new RedisClusterLock(connectionFactories, lockKey, fencingTokenService)

    when:
    def getLock = redisLockManager.acquireWithHeartbeat(redisLock)

    then:
    getLock
    redisLock.isCurrentLockValid()
    redisLockManager.heartbeatTasksByLockId.containsKey(lockKey)

    cleanup:
    redisLockManager.release(redisLock)
  }

  def "should fail to get a lock when there is an already one held"() {
    given:
    def lockKey = "conflict-test-" + UUID.randomUUID()
    def redisLock1 = new RedisClusterLock(connectionFactories, lockKey, fencingTokenService)
    def redisLock2 = new RedisClusterLock(connectionFactories, lockKey, fencingTokenService)

    when:
    def acquired1 = redisLockManager.acquireWithHeartbeat(redisLock1)
    def acquired2 = redisLockManager.acquireWithHeartbeat(redisLock2)

    then:
    acquired1
    !acquired2
    redisLock1.isCurrentLockValid()
    !redisLock2.isCurrentLockValid()

    cleanup:
    redisLockManager.release(redisLock1)
  }

  def "should successfully get lock with timeout"() {
    given:
    def lockKey = "timeout-test-" + UUID.randomUUID()
    def redisLock = new RedisClusterLock(connectionFactories, lockKey, fencingTokenService)

    when:
    def getLock = redisLockManager.acquireWithHeartbeat(redisLock, 5, TimeUnit.SECONDS)

    then:
    getLock
    redisLock.isCurrentLockValid()

    cleanup:
    redisLockManager.release(redisLock)
  }

  def "should timeout when trying to get an already held lock"() {
    given:
    def lockKey = "timeout-conflict-" + UUID.randomUUID()
    def redisLock1 = new RedisClusterLock(connectionFactories, lockKey, fencingTokenService)
    def redisLock2 = new RedisClusterLock(connectionFactories, lockKey, fencingTokenService)

    when:
    redisLockManager.acquireWithHeartbeat(redisLock1)
    def start = System.currentTimeMillis()
    def acquired2 = redisLockManager.acquireWithHeartbeat(redisLock2, 2, TimeUnit.SECONDS)
    def elapsed = System.currentTimeMillis() - start

    then:
    !acquired2
    elapsed >= 1800 // At least 2 seconds
    elapsed < 3000  // But not too much longer

    cleanup:
    redisLockManager.release(redisLock1)
  }


  @Timeout(23)
  def "should renew lock via heartbeat and keep it valid beyond TTL"() {
    given:
    def lockKey = "heartbeat-renewal-" + UUID.randomUUID()
    def redisLock = new RedisClusterLock(connectionFactories, lockKey, fencingTokenService)

    when:
    def acquired = redisLockManager.acquireWithHeartbeat(redisLock)

    then:
    acquired
    redisLock.isCurrentLockValid()

    when:
    Thread.sleep(12_000)// longer than TTL

    then:
    redisLock.isCurrentLockValid()

    when:
    Thread.sleep(8_000)  // Total 20s, well beyond TTL

    then:
    redisLock.isCurrentLockValid()

    cleanup:
    redisLockManager.release(redisLock)
  }

  def "should stop heartbeat and clean up on release"() {
    given:
    def lockKey = "cleanup-test-" + UUID.randomUUID()
    def redisLock = new RedisClusterLock(connectionFactories, lockKey, fencingTokenService)

    when:
    redisLockManager.acquireWithHeartbeat(redisLock)

    then:
    redisLockManager.heartbeatTasksByLockId.containsKey(lockKey)
    redisLock.isCurrentLockValid()

    when:
    redisLockManager.release(redisLock)

    then:
    !redisLockManager.heartbeatTasksByLockId.containsKey(lockKey)
    !redisLock.isCurrentLockValid()
  }

  def "should handle multiple locks independently"() {
    given:
    def lockKeys = (1..5).collect { "multi-lock-${it}-" + UUID.randomUUID() }
    def redisLocks = lockKeys.collect { new RedisClusterLock(connectionFactories, it, fencingTokenService) }

    when:
    def results = redisLocks.collect { redisLockManager.acquireWithHeartbeat(it) }

    then:
    results.every { it }
    redisLocks.every { it.isCurrentLockValid() }
    redisLockManager.heartbeatTasksByLockId.size() == 5

    when:
    // Some locks released
    redisLockManager.release(redisLocks[0])
    redisLockManager.release(redisLocks[2])

    then:
    redisLockManager.heartbeatTasksByLockId.size() == 3
    !redisLocks[0].isCurrentLockValid()
    redisLocks[1].isCurrentLockValid()
    !redisLocks[2].isCurrentLockValid()
    redisLocks[3].isCurrentLockValid()
    redisLocks[4].isCurrentLockValid()

    cleanup:
    redisLocks.each { redisLockManager.release(it) }
  }


  @Timeout(30)
  def "only one thread should acquire the lock at a time"() {
    given:
    def lockKey = "concurrent-single-lock-" + UUID.randomUUID()
    def threadCount = 10
    def executor = Executors.newFixedThreadPool(threadCount)
    def successCount = new AtomicInteger(0) // Thread-safe counter
    def failureCount = new AtomicInteger(0) // Thread-safe counter
    def latch = new CountDownLatch(threadCount)
    def startLatch = new CountDownLatch(1)

    when:
    (1..threadCount).each {
      executor.submit {
        try {
          startLatch.await()
          def redisLock = new RedisClusterLock(connectionFactories, lockKey, fencingTokenService)
          if (redisLockManager.acquireWithHeartbeat(redisLock, 1, TimeUnit.SECONDS)) {
            successCount.incrementAndGet()
            Thread.sleep(1000) // Hold lock to simulate contention
            redisLockManager.release(redisLock)
          } else {
            failureCount.incrementAndGet()
          }
        } finally {
          latch.countDown()
        }
      }
    }

    Thread.sleep(500)
    startLatch.countDown()
    latch.await(25, TimeUnit.SECONDS)
    executor.shutdown()

    then:
    successCount.get() == 1
    failureCount.get() == threadCount - 1
  }

  @Timeout(30)
  def "should handle concurrent operations on different locks"() {
    given:
    def threadCount = 10
    def executor = Executors.newFixedThreadPool(threadCount)
    def successCount = new AtomicInteger(0)
    def latch = new CountDownLatch(threadCount)

    when:
    (1..threadCount).each { i ->
      executor.submit {
        try {
          def lockKey = "concurrent-diff-${i}-" + UUID.randomUUID()
          def redisLock = new RedisClusterLock(connectionFactories, lockKey, fencingTokenService)
          if (redisLockManager.acquireWithHeartbeat(redisLock)) {
            successCount.incrementAndGet()
            Thread.sleep(100)
            redisLockManager.release(redisLock)
          }
        } finally {
          latch.countDown()
        }
      }
    }

    latch.await(25, TimeUnit.SECONDS)
    executor.shutdown()

    then:
    successCount.get() == threadCount
  }


  def "should gracefully handle release of a non-existent lock"() {
    given:
    def lockKey = "non-existent-" + UUID.randomUUID()
    def redisLock = new RedisClusterLock(connectionFactories, lockKey, fencingTokenService)

    when:
    redisLockManager.release(redisLock) // Release without acquiring

    then:
    notThrown(Exception)
    !redisLockManager.heartbeatTasksByLockId.containsKey(lockKey)
  }

  def "should handle double release gracefully"() {
    given:
    def lockKey = "double-release-" + UUID.randomUUID()
    def redisLock = new RedisClusterLock(connectionFactories, lockKey, fencingTokenService)

    when:
    redisLockManager.acquireWithHeartbeat(redisLock)
    redisLockManager.release(redisLock)
    redisLockManager.release(redisLock) // Double release

    then:
    notThrown(Exception)
    !redisLockManager.heartbeatTasksByLockId.containsKey(lockKey)
  }


  @Timeout(60)
  def "should handle rapid acquire/release cycles"() {
    given:
    def cycles = 50
    def successCount = 0

    when:
    (1..cycles).each { i ->
      def lockKey = "rapid-cycle-" + UUID.randomUUID()
      def redisLock = new RedisClusterLock(connectionFactories, lockKey, fencingTokenService)
      if (redisLockManager.acquireWithHeartbeat(redisLock, 2, TimeUnit.SECONDS)) {
        successCount++
        Thread.sleep(10) // Brief hold
        redisLockManager.release(redisLock)
        Thread.sleep(10) // Brief pause
      }
    }

    then:
    successCount == cycles
    redisLockManager.getHeartbeatTasksByLockId().size()==0
  }

  @Unroll
  def "should maintain lock integrity under load with #lockCount locks"() {
    given:
    def lockKeys = (1..lockCount).collect { "load-test-${it}-" + UUID.randomUUID() }
    def redisLocks = lockKeys.collect { new RedisClusterLock(connectionFactories, it, fencingTokenService) }

    when:
    def results = redisLocks.collect { redisLockManager.acquireWithHeartbeat(it) }

    then:
    results.every { it }
    redisLocks.every { it.isCurrentLockValid() }

    when:
    Thread.sleep(15_000) // Wait for 2 heartbeats

    then:
    redisLocks.every { it.isCurrentLockValid() }
    redisLockManager.heartbeatTasksByLockId.size() == lockCount

    cleanup:
    redisLocks.each { redisLockManager.release(it) }

    where:
    lockCount << [5, 10, 20]
  }


  def "should handle lock expiration when heartbeat fails"() {
    given:
    def lockKey = "expiration-test-" + UUID.randomUUID()
    def redisLock = new RedisClusterLock(connectionFactories, lockKey, fencingTokenService)

    when:
    def acquired = redisLockManager.acquireWithHeartbeat(redisLock)

    then:
    acquired
    redisLock.isCurrentLockValid()

    when:
    // Redis containers stop
    [redis1, redis2, redis3]*.stop()

    and:
    Thread.sleep(12_000) // Wait longer than renewal interval

    then:
    // The heartbeat should fail and clean up the lock
    // we should check it doesn't crash at least
    notThrown(Exception)

    cleanup:
    // Restart containers for other tests
    [redis1, redis2, redis3]*.start()
    // Recreate connection factories
    connectionFactories.clear()
    connectionFactories.addAll(createConnectionFactories([redis1, redis2, redis3]))
  }



  def "should track active locks correctly"() {
    given:
    def acquiredLocks = (1..3).collect {
      def lockKey = "tracking-${it}-" + UUID.randomUUID()
      def lock = new RedisClusterLock(connectionFactories, lockKey, fencingTokenService)
      redisLockManager.acquireWithHeartbeat(lock)
      return lock
    }

    expect:
    redisLockManager.heartbeatTasksByLockId.size() == 3

    when:
    redisLockManager.release(acquiredLocks[1])

    then:
    redisLockManager.heartbeatTasksByLockId.size() == 2

    cleanup:
    acquiredLocks.each { redisLockManager.release(it) }
  }



  def "should clean up all locks on shutdown"() {
    given:
    def lockKeys = (1..3).collect { "shutdown-test-${it}-" + UUID.randomUUID() }
    def redisLocks = lockKeys.collect { new RedisClusterLock(connectionFactories, it, fencingTokenService) }

    when:
    redisLocks.each { redisLockManager.acquireWithHeartbeat(it) }

    then:
    redisLockManager.getHeartbeatTasksByLockId().size() == 3
    redisLocks.every { it.isCurrentLockValid() }

    when:
    redisLockManager.shutdown()

    then:
    redisLockManager.getHeartbeatTasksByLockId().isEmpty()

    and:
    redisLockManager.getHeartbeatScheduler().isShutdown()
  }
}
