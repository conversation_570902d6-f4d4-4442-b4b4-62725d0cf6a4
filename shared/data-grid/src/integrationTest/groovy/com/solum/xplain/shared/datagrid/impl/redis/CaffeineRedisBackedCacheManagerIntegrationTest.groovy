package com.solum.xplain.shared.datagrid.impl.redis

import com.solum.xplain.core.helper.RedisIntegrationSpecification
import com.solum.xplain.shared.datagrid.DataGrid
import com.solum.xplain.shared.datagrid.LocalCacheReplica
import java.time.Duration
import java.util.concurrent.CountDownLatch
import java.util.concurrent.TimeUnit
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.redis.cache.RedisCache
import org.springframework.data.redis.cache.RedisCacheManager
import org.springframework.data.redis.core.RedisTemplate
import org.springframework.data.redis.listener.RedisMessageListenerContainer
import spock.util.concurrent.PollingConditions

@RedisTestConfiguration
class CaffeineRedisBackedCacheManagerIntegrationTest extends RedisIntegrationSpecification {

  @Autowired
  RedisTemplate<String, Object> redisTemplate
  @Autowired
  RedisMessageListenerContainer redisMessageListenerContainer

  RedisCacheMessagePublisher publisher = new RedisCacheMessagePublisher(new RedisPubSubTopic<>(redisTemplate, redisMessageListenerContainer, "cache-invalidation"))

  @Autowired
  DataGrid dataGrid

  def redisCacheManager = Mock(RedisCacheManager)
  def defaultTtl = Duration.ofMinutes(30)
  def idleTtl = Duration.ofMinutes(10)

  def "should initialize with local cache replicas"() {
    given: "Local cache configurations"
    def localCache1 = new LocalCacheReplica("cache1", 100, defaultTtl, idleTtl, true)
    def localCache2 = new LocalCacheReplica("cache2", 200, defaultTtl, idleTtl, false)
    def localCaches = [localCache1, localCache2]
    def redisCache1 = Mock(RedisCache)
    def redisCache2 = Mock(RedisCache)
    redisCacheManager.getCache("cache1") >> redisCache1
    redisCacheManager.getCache("cache2") >> redisCache2

    when: "creating the cache manager"
    def cacheManager = new CaffeineRedisBackedCacheManager(
      localCaches,
      redisCacheManager,
      publisher
      )

    then: "it should create caches for each local cache config"
    cacheManager.getCache("cache1") != null
    cacheManager.getCache("cache2") != null
  }

  def "should be able to get shared cache()"() {
    given: "a shared cache"
    def redisCache = Mock(RedisCache)

    and: "redis cache manager has a cache"
    redisCacheManager.getCache("redisCache") >> redisCache

    when: "creating the cache manager and requesting a non-local cache"
    def cacheManager = new CaffeineRedisBackedCacheManager(
      [],
      redisCacheManager,
      publisher
      )
    def cache = cacheManager.getCache("redisCache")

    then: "it should delegate to the redis cache manager"
    cache == redisCache
  }

  def "should return combined cache names"() {
    given: "local and shared cache configured"
    def localCache1 = new LocalCacheReplica("testCache1", 100, defaultTtl, idleTtl, true)
    def localCache2 = new LocalCacheReplica("testCache2", 100, defaultTtl, idleTtl, true)
    def localCaches = [localCache1, localCache2]
    def redisCache1 = Mock(RedisCache)
    def redisCache2 = Mock(RedisCache)
    redisCacheManager.getCache("testCache1") >> redisCache1
    redisCacheManager.getCache("testCache2") >> redisCache2

    and: "redis cache manager has cache names"
    def redisCacheNames = ["testCache1", "testCache2"] as Set
    redisCacheManager.getCacheNames() >> redisCacheNames

    when: "creating the cache manager and requesting cache names"
    def cacheManager = new CaffeineRedisBackedCacheManager(
      localCaches,
      redisCacheManager,
      publisher
      )
    def cacheNames = cacheManager.getCacheNames()

    then: "it should return set of combined cache names"
    cacheNames.containsAll(["testCache1", "testCache2"])
    cacheNames.size() == 2
  }

  def "should invalidate key in other local caches when autoUpdate=true and put is called with a new value"() {
    given: "cache configuration"
    def cacheName = "testCache"
    def key1 = "testKey"
    def value1 = "testValue"
    def newValue = "newValue"

    def localCache = new LocalCacheReplica(cacheName, 100, defaultTtl, idleTtl, true)
    def localCaches = [localCache]
    def redisCache = Mock(RedisCache)
    redisCacheManager.getCache(cacheName) >>> redisCache

    and: "setup latches"
    def valuesInserted = new CountDownLatch(1)
    def cacheUpdated = new CountDownLatch(1)
    def checkCompleted = new CountDownLatch(1)
    def finalResults

    when: "running two threads in parallel"
    def thread1 = Thread.start {
      def cacheManager1 = new CaffeineRedisBackedCacheManager(
        localCaches,
        redisCacheManager,
        publisher
        )

      def listener1 = new RedisCacheInvalidationMessageListener(dataGrid, cacheManager1)

      def cache1 = cacheManager1.getCache(cacheName)

      cache1.put(key1, value1)

      valuesInserted.countDown()

      cacheUpdated.await(5, TimeUnit.SECONDS)

      def waitForClearing = new PollingConditions(timeout: 5, delay: 0.1)

      waitForClearing.eventually {
        assert cache1.get(key1)?.get() == null
      }

      finalResults = cache1.get(key1)?.get()

      listener1.destroy()

      checkCompleted.countDown()
    }

    def thread2 = Thread.start {
      def cacheManager2 = new CaffeineRedisBackedCacheManager(
        localCaches,
        redisCacheManager,
        publisher
        )

      def listener2 = new RedisCacheInvalidationMessageListener(dataGrid, cacheManager2)

      def cache2 = cacheManager2.getCache(cacheName)

      cache2.put(key1, value1)

      valuesInserted.await(5, TimeUnit.SECONDS)

      cache2.put(key1, newValue)

      cacheUpdated.countDown()

      checkCompleted.await(5, TimeUnit.SECONDS)

      listener2.destroy()
    }

    thread1.join(15000)
    thread2.join(15000)

    then: "all values should be cleared in cache1"
    finalResults == null
  }

  def "should invalidate key in other local caches when autoUpdate=true and evict is called"() {
    given: "cache configuration"
    def cacheName = "testCache"
    def key1 = "testKey"
    def value1 = "testValue"

    def localCache = new LocalCacheReplica(cacheName, 100, defaultTtl, idleTtl, true)
    def localCaches = [localCache]
    def redisCache = Mock(RedisCache)
    redisCacheManager.getCache(cacheName) >>> redisCache

    and: "setup latches"
    def valuesInserted = new CountDownLatch(1)
    def cacheUpdated = new CountDownLatch(1)
    def checkCompleted = new CountDownLatch(1)
    def finalResults

    when: "running two threads in parallel"
    def thread1 = Thread.start {
      def cacheManager1 = new CaffeineRedisBackedCacheManager(
        localCaches,
        redisCacheManager,
        publisher
        )

      def listener1 = new RedisCacheInvalidationMessageListener(dataGrid, cacheManager1)

      def cache1 = cacheManager1.getCache(cacheName)

      cache1.put(key1, value1)

      valuesInserted.countDown()

      cacheUpdated.await(5, TimeUnit.SECONDS)

      def waitForClearing = new PollingConditions(timeout: 5, delay: 0.1)

      waitForClearing.eventually {
        assert cache1.get(key1)?.get() == null
      }

      finalResults = cache1.get(key1)?.get()

      listener1.destroy()

      checkCompleted.countDown()
    }

    def thread2 = Thread.start {
      def cacheManager2 = new CaffeineRedisBackedCacheManager(
        localCaches,
        redisCacheManager,
        publisher
        )

      def listener2 = new RedisCacheInvalidationMessageListener(dataGrid, cacheManager2)

      def cache2 = cacheManager2.getCache(cacheName)

      cache2.put(key1, value1)

      valuesInserted.await(5, TimeUnit.SECONDS)

      cache2.evict(key1)

      cacheUpdated.countDown()

      checkCompleted.await(5, TimeUnit.SECONDS)

      listener2.destroy()
    }

    thread1.join(15000)
    thread2.join(15000)

    then: "all values should be cleared in cache1"
    finalResults == null
  }


  def "should clear all local caches when clear is called in one instance "() {
    given: "cache configuration"
    def cacheName = "testCache"
    def key1 = "clearKey1"
    def value1 = "clearValue1"
    def key2 = "clearKey2"
    def value2 = "clearValue2"

    def localCache = new LocalCacheReplica(cacheName, 100, defaultTtl, idleTtl, true)
    def localCaches = [localCache]
    def redisCache = Mock(RedisCache)
    redisCacheManager.getCache(cacheName) >>> redisCache

    and: "setup latches"
    def cacheCleared = new CountDownLatch(1)
    def checkCompleted = new CountDownLatch(1)
    def clearResults = [:]

    when: "running two threads in parallel"
    def thread1 = Thread.start {
      def cacheManager1 = new CaffeineRedisBackedCacheManager(
        localCaches,
        redisCacheManager,
        publisher
        )

      def listener1 = new RedisCacheInvalidationMessageListener(dataGrid, cacheManager1)

      def cache1 = cacheManager1.getCache(cacheName)

      cache1.put(key1, value1)
      cache1.put(key2, value2)

      cacheCleared.await(5, TimeUnit.SECONDS)

      def waitForClearing = new PollingConditions(timeout: 5, delay: 0.1)

      waitForClearing.eventually {
        assert cache1.get(key1)?.get() == null
        assert cache1.get(key2)?.get() == null
      }

      clearResults[key1] = cache1.get(key1)?.get()
      clearResults[key2] = cache1.get(key2)?.get()

      listener1.destroy()

      checkCompleted.countDown()
    }

    def thread2 = Thread.start {
      def cacheManager2 = new CaffeineRedisBackedCacheManager(
        localCaches,
        redisCacheManager,
        publisher
        )

      def listener2 = new RedisCacheInvalidationMessageListener(dataGrid, cacheManager2)

      def cache2 = cacheManager2.getCache(cacheName)

      cache2.put(key1, value1)
      cache2.put(key2, value2)

      cache2.clear()

      cacheCleared.countDown()

      checkCompleted.await(5, TimeUnit.SECONDS)

      listener2.destroy()
    }

    thread1.join(15000)
    thread2.join(15000)

    then: "all values should be cleared in the other cache"
    clearResults[key1] == null
    clearResults[key2] == null
  }
}
