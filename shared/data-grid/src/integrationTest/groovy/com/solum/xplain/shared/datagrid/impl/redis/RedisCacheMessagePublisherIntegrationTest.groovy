package com.solum.xplain.shared.datagrid.impl.redis

import com.solum.xplain.core.helper.RedisIntegrationSpecification
import com.solum.xplain.shared.datagrid.PubSubTopic
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.redis.core.RedisTemplate
import org.springframework.data.redis.listener.RedisMessageListenerContainer
import org.springframework.test.util.ReflectionTestUtils

@RedisTestConfiguration
class RedisCacheMessagePublisherIntegrationTest extends RedisIntegrationSpecification {

  @Autowired
  RedisTemplate<String, Object> redisTemplate
  @Autowired
  RedisMessageListenerContainer redisMessageListenerContainer

  def publisher = new RedisCacheMessagePublisher(new RedisPubSubTopic<>(redisTemplate, redisMessageListenerContainer, "cache-invalidation"))

  def invalidationTopic = Mock(PubSubTopic)
  def sourceId = "testSource"

  def setup() {
    ReflectionTestUtils.setField(publisher , "invalidationTopic", invalidationTopic)
  }

  def "should publish invalidation message"() {
    given:
    def cacheName = "testCache"
    def key = "testKey"

    when:
    publisher.publishInvalidateKeyMessage(cacheName, key, sourceId)

    then:
    1 * invalidationTopic.publish({ message ->
      message.cacheName() == cacheName && message.key() == key
    })
  }

  def "should publish invalidation message with null key for clear operations"() {
    given:
    def cacheName = "testCache"

    when:
    publisher.publishInvalidateAllMessage(cacheName, sourceId)

    then:
    1 * invalidationTopic.publish({ message ->
      message.cacheName() == cacheName && message.key() == null
    })
  }
}
