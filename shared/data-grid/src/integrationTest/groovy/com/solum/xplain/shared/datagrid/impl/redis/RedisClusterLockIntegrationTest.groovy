package com.solum.xplain.shared.datagrid.impl.redis

import com.redis.testcontainers.RedisContainer
import com.solum.xplain.core.helper.RedisIntegrationSpecification
import com.solum.xplain.shared.datagrid.FencingTokenService
import com.solum.xplain.shared.datagrid.event.ClusterProperties
import java.util.concurrent.CountDownLatch
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.redis.connection.RedisStandaloneConfiguration
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory
import spock.lang.Shared
import spock.lang.Unroll

@SpringBootTest
@RedisTestConfiguration
@EnableConfigurationProperties([ClusterProperties])
class RedisClusterLockIntegrationTest extends RedisIntegrationSpecification {

  @Shared
  static RedisContainer redis1 = new RedisContainer("redis:7.4")
  @Shared
  static RedisContainer redis2 = new RedisContainer("redis:7.4")
  @Shared
  static RedisContainer redis3 = new RedisContainer("redis:7.4")

  @Shared
  List<JedisConnectionFactory> connectionFactories = []
  @Autowired
  FencingTokenService fencingTokenService

  RedisClusterLock instanceOne
  RedisClusterLock instanceTwo
  String lockKey

  //Run once
  def setupSpec() {
    [redis1, redis2, redis3]*.start()
    connectionFactories.addAll(createConnectionFactories([redis1, redis2, redis3]))
  }

  def cleanupSpec() {
    connectionFactories?.each { factory ->
      try {
        factory.destroy()
      } catch (Exception ignored) {}
    }
    [redis1, redis2, redis3]*.stop()
  }

  private static List<JedisConnectionFactory> createConnectionFactories(List<RedisContainer> containers) {
    containers.collect { container ->
      def config = new RedisStandaloneConfiguration(container.host, container.firstMappedPort)
      def factory = new JedisConnectionFactory(config)
      factory.afterPropertiesSet()
      return factory
    }
  }

  //Run before each test
  def setup() {
    lockKey = "test-cluster-lock-" + UUID.randomUUID()
    //represent application nodes
    instanceOne = new RedisClusterLock(connectionFactories, lockKey, fencingTokenService)
    instanceTwo = new RedisClusterLock(connectionFactories, lockKey, fencingTokenService)
  }

  def cleanup() {
    [instanceOne, instanceTwo].each { lock ->
      try {
        lock?.unlockAll()
      } catch (Exception ignored) {}
    }
  }

  def "should acquire and release lock successfully with increasing fencing tokens"() {
    when:
    def firstAcquired = instanceOne.tryLock()
    def firstToken = instanceOne.getCurrentFencingToken()

    then:
    firstAcquired
    firstToken > 0

    when:
    instanceOne.unlock()
    def secondAcquired = instanceOne.tryLock()
    def secondToken = instanceOne.getCurrentFencingToken()

    then:
    secondAcquired
    secondToken > firstToken
  }

  def "should not allow 2 concurrent access from different lock instances"() {
    given:
    instanceOne.tryLock()
    def firstToken = instanceOne.getCurrentFencingToken()

    when:
    def getLockInstance2 = instanceTwo.tryLock()

    then:
    !getLockInstance2 // As the first lock is already acquired

    when:
    instanceOne.unlock()

    then:
    instanceTwo.tryLock()
    instanceTwo.getCurrentFencingToken() > firstToken
    !instanceOne.validateFencingToken(firstToken)
  }

  def "should timeout when lock is held and timeout is specified"() {
    given:
    instanceOne.tryLock()

    when:
    def acquired = instanceTwo.tryLock(500, TimeUnit.MILLISECONDS)

    then:
    !acquired
  }

  def "should run lockInterruptibly without exception"() {
    when:
    instanceOne.lockInterruptibly()

    then:
    noExceptionThrown()

    when:
    instanceOne.unlock()

    then:
    instanceTwo.tryLock()
  }

  def "should handle multiple concurrent lock attempts with fencing tokens"() {
    given:
    def threadCount = 5
    def executor = Executors.newFixedThreadPool(threadCount)
    def acquiredTokens = Collections.synchronizedList([])
    def latch = new CountDownLatch(threadCount)

    when:
    def futures = (1..threadCount).collect { i ->
      executor.submit({
        // each thread has its own lock instance
        def threadLock = new RedisClusterLock(connectionFactories, lockKey, fencingTokenService)
        try {
          if (threadLock.tryLock(1, TimeUnit.SECONDS)) {
            def token = threadLock.getCurrentFencingToken()
            acquiredTokens.add(token)
            Thread.sleep(30)
            threadLock.unlock()
          }
        } finally {
          latch.countDown()
        }
      })
    }

    and:
    latch.await(5, TimeUnit.SECONDS)

    then:
    assert acquiredTokens.every { it != null && it > 0 }
    assert acquiredTokens.unique().size() == acquiredTokens.size()
    assert acquiredTokens == acquiredTokens.sort(false) // compare with sorted copy

    def maxToken = acquiredTokens.max()
    acquiredTokens.each { token ->
      if (token == maxToken) {
        assert fencingTokenService.validateToken(lockKey, token as long)
      } else {
        assert !fencingTokenService.validateToken(lockKey, token as long)
      }
    }

    cleanup:
    executor.shutdown()
  }

  def "should handle Redis instance failures gracefully"() {
    given:
    def acquired = instanceOne.tryLock() // All redis instances are available

    expect:
    acquired

    when:
    redis3.stop() // When one Redis instance failure

    and:
    instanceOne.unlock()
    def reacquired = instanceOne.tryLock()

    then: // lock acquisition should still succeed
    reacquired

    cleanup:
    redis3.start()
  }

  @Unroll
  def "can lock with different keys #testKey"() {
    given:
    def lock1 = new RedisClusterLock(connectionFactories, testKey + "-1", fencingTokenService)
    def lock2 = new RedisClusterLock(connectionFactories, testKey + "-2", fencingTokenService)

    when:
    def acquired1 = lock1.tryLock()
    def acquired2 = lock2.tryLock()

    then: // Both locks should be acquired successfully
    acquired1
    acquired2

    cleanup:
    [lock1, lock2]*.unlockAll()

    where:
    testKey << ["redis-cluster-lock", "1244", "!@#"]
  }

  def "should handle unlockAll method correctly"() {
    given:
    def lock1 = new RedisClusterLock(connectionFactories, lockKey + "-1", fencingTokenService)
    def lock2 = new RedisClusterLock(connectionFactories, lockKey + "-2", fencingTokenService)

    lock1.tryLock()
    lock2.tryLock()

    when:
    lock1.unlockAll()
    lock2.unlockAll()

    then:
    noExceptionThrown()

    when:
    def reacquired1 = lock1.tryLock()
    def reacquired2 = lock2.tryLock()

    then:
    reacquired1
    reacquired2
  }

  def "should handle rapid lock get and release"() {
    given:
    def lockAsk = 10
    def allSuccessful = true

    when:
    for (int i = 0; i < lockAsk; i++) {
      if (!instanceOne.tryLock()) {
        allSuccessful = false
        break
      }
      instanceOne.unlock()
    }

    then:
    allSuccessful
  }


  def "should handle fencing token validation correctly"() {
    given:
    def testLockKey = "validation-test-" + UUID.randomUUID()
    def lock = new RedisClusterLock(connectionFactories, testLockKey, fencingTokenService)

    when:
    lock.tryLock()
    def token1 = lock.getCurrentFencingToken()

    then:
    lock.validateFencingToken(token1)
    lock.isCurrentLockValid()

    when:
    lock.unlock()

    then:
    !lock.isCurrentLockValid()
    lock.isLockExpired()

    when:
    lock.tryLock()
    def token2 = lock.getCurrentFencingToken()

    then:
    token2 > token1
    !fencingTokenService.validateToken(testLockKey, token1)
    fencingTokenService.validateToken(testLockKey, token2)

    cleanup:
    lock?.unlockAll()
  }


  def "should handle concurrent fencing token operations"() {
    given: "multiple threads accessing fencing tokens"
    def testResource = "concurrent-fencing-" + UUID.randomUUID()
    def threadCount = 10
    def executor = Executors.newFixedThreadPool(threadCount)
    def generatedTokens = Collections.synchronizedList([])
    def latch = new CountDownLatch(threadCount)

    when:
    //multiple threads generate tokens concurrently
    def futures = (1..threadCount).collect { i ->
      executor.submit({
        try {
          def token = fencingTokenService.generateToken(testResource)
          generatedTokens.add(token)
          println "Thread-${i} generated fencing token: ${token}"
        } finally {
          latch.countDown()
        }
      })
    }

    latch.await(5, TimeUnit.SECONDS)

    then:
    generatedTokens.size() == threadCount
    generatedTokens.every { it > 0 }
    generatedTokens.unique().size() == threadCount // All unique
    generatedTokens.sort() == generatedTokens // Should be in order due to atomic increment

    cleanup:
    executor.shutdown()
  }

  def "should verify lock name and key consistency"() {
    given:
    def baseName = "consistency-test"
    def lock1 = new RedisClusterLock(connectionFactories, baseName, fencingTokenService)
    def lock2 = new RedisClusterLock(connectionFactories, baseName, fencingTokenService)

    when:
    // check lock names
    def name1 = lock1.getName()
    def name2 = lock2.getName()

    then:
    // names should match the base name
    name1 == baseName
    name2 == baseName
    name1 == name2

    when:
    //we acquire one lock
    def acquired1 = lock1.tryLock()
    def acquired2 = lock2.tryLock()

    then:
    //only one should succeed (same key)
    acquired1
    !acquired2

    cleanup:
    [lock1, lock2]*.unlockAll()
  }

  def "should verify lock prefix is applied correctly"() {
    given:
    def testKey = "prefix-test"
    def expectedPrefix = "LOCK_VALUE__"

    when:
    def lock = new RedisClusterLock(connectionFactories, testKey, fencingTokenService)
    def lockName = lock.getName()

    then:
    //the public name should not have the prefix
    lockName == testKey

    and:
    // the internal lock key should use the prefix
    lockName != expectedPrefix + testKey
  }


  def "should verify cleanup method"() {
    given:
    def testResource = "cleanup-test-" + UUID.randomUUID()

    when:
    def token1 = fencingTokenService.generateToken(testResource)

    and:
    // cleanup currently a no-op
    fencingTokenService.cleanup(testResource)

    and:
    def token2 = fencingTokenService.generateToken(testResource)

    then:
    token1 > 0 == fencingTokenService.isValidToken(token1)
    token2 > token1

    and: "current token should still be accessible"
    fencingTokenService.getCurrentToken(testResource) == token2
  }

  @Unroll
  def "should verify getTokenName returns correct format for resourceId: '#resourceId'"() {
    when: "getTokenName is called"
    def tokenName = fencingTokenService.getTokenName(resourceId)

    then: "it returns the correct format"
    tokenName.startsWith("FENCING_TOKEN__")
    tokenName.endsWith(resourceId)
    tokenName == "FENCING_TOKEN__" + resourceId

    where:
    resourceId << ["simple", "resource_with-dashes", "Numbers123", "Special!@#&*"]
  }

  def "should renew lock correctly"() {
    given:
    def lock = new RedisClusterLock(connectionFactories, lockKey, fencingTokenService)
    assert lock.tryLock()
    def initialToken = lock.getCurrentFencingToken()

    when:
    Thread.sleep(9 * 1000) // TTL is 10 so we sleep for 9 seconds
    def renewed = lock.renewLock()
    def tokenAfterRenew = lock.getCurrentFencingToken()

    then:
    renewed
    tokenAfterRenew == initialToken
    lock.isCurrentLockValid()
  }

  def "should not renew lock"() {
    given:
    def lock = new RedisClusterLock(connectionFactories, lockKey, fencingTokenService)
    assert lock.tryLock()
    def initialToken = lock.getCurrentFencingToken()

    when:
    Thread.sleep(10 * 1000) // TTL is 10
    def renewed = lock.renewLock()
    def tokenAfterRenew = lock.getCurrentFencingToken()

    then:
    !renewed
    lock.isLockExpired()
  }

  def "should not renew lock with invalid fencing token"() {
    given:
    def lock = new RedisClusterLock(connectionFactories, lockKey, fencingTokenService)
    assert lock.tryLock()
    def initialToken = lock.getCurrentFencingToken()

    when:
    Thread.sleep(9 * 1000) // TTL is 10 so we sleep for 9 seconds
    fencingTokenService.generateToken(lockKey) // generate a new token
    def renewed = lock.renewLock()
    def tokenAfterRenew = lock.getCurrentFencingToken()

    then:
    !renewed
    tokenAfterRenew == null
    lock.isLockExpired()
  }

  def "should renew lock with one Redis instance down"() {
    given:
    def lock = new RedisClusterLock(connectionFactories, lockKey, fencingTokenService)
    assert lock.tryLock()
    def initialToken = lock.getCurrentFencingToken()

    when:
    // Let TTL get close to expiration
    Thread.sleep(9000)
    // Simulate 1 Redis instance failure
    redis3.stop()

    def renewed = lock.renewLock()
    def tokenAfterRenew = lock.getCurrentFencingToken()

    then:
    renewed
    tokenAfterRenew == initialToken
    lock.isCurrentLockValid()

    cleanup:
    redis3.start()
  }

  def "should fail to renew lock with only one Redis instance up"() {
    given:
    def lock = new RedisClusterLock(connectionFactories, lockKey, fencingTokenService)
    assert lock.tryLock()
    def initialToken = lock.getCurrentFencingToken()

    when:
    Thread.sleep(9000)

    // quorum lost
    redis2.stop()
    redis3.stop()

    def renewed = lock.renewLock()
    def tokenAfterRenew = lock.getCurrentFencingToken()

    then:
    !renewed
    tokenAfterRenew == null
    lock.isLockExpired()

    cleanup:
    redis2.start()
    redis3.start()
  }
}
