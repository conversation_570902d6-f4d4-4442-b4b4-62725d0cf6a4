package com.solum.xplain.shared.datagrid.impl.redis

import com.solum.xplain.core.helper.RedisIntegrationSpecification
import com.solum.xplain.shared.datagrid.DataGrid
import com.solum.xplain.shared.datagrid.topic.Subscription
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.cache.CacheManager
import org.springframework.test.util.ReflectionTestUtils

@RedisTestConfiguration
class RedisCacheInvalidationMessageListenerIntegrationTest extends RedisIntegrationSpecification {

  @Autowired
  DataGrid dataGrid

  def cacheManager = Mock(CacheManager)

  def cache = Mock(CaffeineRedisBackedSpringCache)

  def invalidationSubscription = Mock(Subscription)
  def listener

  def setup() {
    listener = Spy(new RedisCacheInvalidationMessageListener(dataGrid, cacheManager))

    ReflectionTestUtils.setField(listener, "invalidationSubscription", invalidationSubscription)
    ReflectionTestUtils.setField(listener, "cacheManager", cacheManager)
  }

  def "should unsubscribe on destroy"() {
    when:
    listener.destroy()

    then:
    1 * invalidationSubscription.unsubscribe()
  }

  def "should handle invalidation message"() {
    given:
    def cacheName = "testCache"
    def key = "testKey"
    def sourceId = "sourceId"
    def message = new RedisCacheInvalidationMessage(cacheName, key, sourceId)
    cacheManager.getCache(cacheName) >> cache
    cache.getSourceId() >> "testId"

    when:
    listener.onInvalidationMessage(message)

    then:
    1 * cache.evictLocalCacheEntry(key)
  }

  def "should handle invalidation message with null key (clear)"() {
    given:
    def cacheName = "testCache"
    def sourceId = "sourceId"
    def message = new RedisCacheInvalidationMessage(cacheName, sourceId)
    cacheManager.getCache(cacheName) >> cache
    cache.getSourceId() >> "testId"

    when:
    listener.onInvalidationMessage(message)

    then:
    1 * cache.clearLocalCacheOnly()
  }

  def "should handle invalidation message for non-existent cache"() {
    given:
    def cacheName = "nonExistentCache"
    def key = "testKey"
    def sourceId = "sourceId"
    def message = new RedisCacheInvalidationMessage(cacheName, key, sourceId)
    cacheManager.getCache(cacheName) >> null
    cache.getSourceId() >> "testId"

    when:
    listener.onInvalidationMessage(message)

    then:
    0 * cache._
  }
}
