package com.solum.xplain.shared.datagrid.impl.redis;

import static com.solum.xplain.shared.datagrid.impl.redis.RedisCacheMessagePublisher.INVALIDATION_TOPIC;

import com.solum.xplain.shared.datagrid.DataGrid;
import com.solum.xplain.shared.datagrid.PubSubTopic;
import com.solum.xplain.shared.datagrid.impl.redis.nearcache.AbstractCaffeineRedisBackedCache;
import com.solum.xplain.shared.datagrid.topic.Subscription;
import jakarta.annotation.PreDestroy;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.jspecify.annotations.NullMarked;
import org.springframework.cache.CacheManager;

/** Listener to process syncing changes between caches across clusters. */
@Slf4j
@NullMarked
public class RedisCacheInvalidationMessageListener {

  private final CacheManager cacheManager;

  private final Subscription invalidationSubscription;

  public RedisCacheInvalidationMessageListener(DataGrid dataGrid, CacheManager cacheManager) {
    this.cacheManager = cacheManager;

    PubSubTopic<RedisCacheInvalidationMessage> invalidationTopic =
        dataGrid.getPubSubTopic(INVALIDATION_TOPIC);
    this.invalidationSubscription = invalidationTopic.subscribe(this::onInvalidationMessage);

    log.info("Subscribed to cache topics: {}", INVALIDATION_TOPIC);
  }

  /** Clean up the subscriptions when the service is destroyed. */
  @PreDestroy
  public void destroy() {
    invalidationSubscription.unsubscribe();
    log.info("Unsubscribed from cache topics");
  }

  /**
   * Handle invalidation messages only if the cache sourceId is not the same as the message sourceId
   * and the cache of type AbstractWriteThroughCache.
   */
  public void onInvalidationMessage(RedisCacheInvalidationMessage message) {
    Optional.ofNullable(cacheManager.getCache(message.cacheName()))
        .filter(AbstractCaffeineRedisBackedCache.class::isInstance)
        .map(AbstractCaffeineRedisBackedCache.class::cast)
        .filter(it -> !message.sourceId().equals(it.getSourceId()))
        .ifPresent(
            cache -> {
              switch (message.operation()) {
                case INVALIDATE_ALL -> cache.clearLocalCacheOnly();
                case INVALIDATE_KEY -> {
                  if (message.key() == null) {
                    log.warn(
                        "Received Invalidation key message with empty Key, cacheName {}",
                        message.cacheName());
                  } else {
                    cache.evictLocalCacheEntry(message.key());
                  }
                }
              }
            });
  }
}
