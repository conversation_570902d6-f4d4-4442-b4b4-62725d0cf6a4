package com.solum.xplain.shared.datagrid.impl.redis;

import java.io.Serializable;
import org.jspecify.annotations.NullMarked;
import org.jspecify.annotations.Nullable;

/**
 * Message sent to invalidate a cache entry or clear the cache.
 *
 * <p>Used for syncing cache invalidations across clusters.
 */
@NullMarked
public record RedisCacheInvalidationMessage(
    String cacheName, CacheInvalidationOperation operation, @Nullable String key, String sourceId)
    implements Serializable {

  public enum CacheInvalidationOperation {
    INVALIDATE_KEY,
    INVALIDATE_ALL
  }

  /**
   * Constructor for invalidate key operation.
   *
   * @param cacheName the name of the cache.
   * @param key the key to be invalidated.
   * @param sourceId the id of the source cache to prevent duplicate processing.
   */
  public RedisCacheInvalidationMessage(String cacheName, String key, String sourceId) {
    this(cacheName, CacheInvalidationOperation.INVALIDATE_KEY, key, sourceId);
  }

  /**
   * Constructor for invalidate all operation.
   *
   * @param cacheName the name of the cache.
   * @param sourceId the id of the source cache to prevent duplicate processing.
   */
  public RedisCacheInvalidationMessage(String cacheName, String sourceId) {
    this(cacheName, CacheInvalidationOperation.INVALIDATE_ALL, null, sourceId);
  }
}
