package com.solum.xplain.shared.datagrid.impl.redis;

import com.github.benmanes.caffeine.cache.Expiry;
import com.solum.xplain.shared.datagrid.LocalCacheReplica;
import java.time.Duration;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import org.jspecify.annotations.NullMarked;

/**
 * Caffeine expiry policy for local cache replicas to support custom ttls. If a custom ttl is
 * specified for a key, it will be used instead of the default ttl.
 */
@NullMarked
public class CaffeineRedisBackedCacheExpiryPolicy<K, V> implements Expiry<K, V> {

  private final LocalCacheReplica config;
  private final Map<K, Duration> customTtls;
  private final Map<K, Long> createTimes = new ConcurrentHashMap<>();

  public CaffeineRedisBackedCacheExpiryPolicy(
      LocalCacheReplica config, Map<K, Duration> customTtls) {
    this.config = config;
    this.customTtls = customTtls;
  }

  @Override
  public long expireAfterCreate(K key, V value, long currentTime) {
    createTimes.put(key, currentTime);
    Duration ttl = customTtls.getOrDefault(key, config.ttl());
    Duration idleTtl = config.idleTtl();
    long ttlNanos = ttl.equals(Duration.ZERO) ? Long.MAX_VALUE : ttl.toNanos();
    long idleTtlNanos = idleTtl.equals(Duration.ZERO) ? Long.MAX_VALUE : idleTtl.toNanos();
    // On creation, we set the expiry to the minimum of the ttl and idle ttl.
    return Math.min(ttlNanos, idleTtlNanos);
  }

  @Override
  public long expireAfterUpdate(K key, V value, long currentTime, long currentDuration) {
    // If we update a value, we use the same logic as on creation.
    return expireAfterCreate(key, value, currentTime);
  }

  @Override
  public long expireAfterRead(K key, V value, long currentTime, long currentDuration) {
    // If we read a value, it is no longer idle, so we can use the original ttl directly based on
    // the create time.
    Duration ttl = customTtls.getOrDefault(key, config.ttl());
    long ttlNanos = ttl.equals(Duration.ZERO) ? Long.MAX_VALUE : ttl.toNanos();
    return Math.max(0, ttlNanos - (currentTime - createTimes.getOrDefault(key, currentTime)));
  }

  public void addCustomTtl(K key, Duration ttl) {
    if (ttl.compareTo(config.ttl()) < 0) {
      // Only worth keeping custom TTLs that are shorter than the default local replica TTL
      customTtls.put(key, ttl);
    }
  }

  void removeCustomTtl(K key) {
    customTtls.remove(key);
  }
}
