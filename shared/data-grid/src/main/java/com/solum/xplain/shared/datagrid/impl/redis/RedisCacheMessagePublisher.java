package com.solum.xplain.shared.datagrid.impl.redis;

import com.solum.xplain.shared.datagrid.PubSubTopic;
import lombok.extern.slf4j.Slf4j;
import org.jspecify.annotations.NullMarked;

/** A publisher to publish invalidation messages using DataGrid topics. */
@Slf4j
@NullMarked
public class RedisCacheMessagePublisher {

  private final PubSubTopic<RedisCacheInvalidationMessage> invalidationTopic;
  public static final String INVALIDATION_TOPIC = "cache-invalidation";

  public RedisCacheMessagePublisher(PubSubTopic<RedisCacheInvalidationMessage> topic) {
    this.invalidationTopic = topic;
    log.info("Initialized cache publisher with topics: {}", INVALIDATION_TOPIC);
  }

  /**
   * Publish a message to invalidate a specific key in a cache.
   *
   * @param cacheName the name of the cache
   * @param key the key to be invalidated
   * @param sourceId the sourceId of the cache publishing the message
   */
  public void publishInvalidateKeyMessage(String cacheName, String key, String sourceId) {
    RedisCacheInvalidationMessage message =
        new RedisCacheInvalidationMessage(cacheName, key, sourceId);
    invalidationTopic.publish(message);
  }

  /**
   * Publish a message to invalidate all entries in a cache.
   *
   * @param cacheName the name of the cache
   * @param sourceId the sourceId of the cache publishing the message
   */
  public void publishInvalidateAllMessage(String cacheName, String sourceId) {
    RedisCacheInvalidationMessage message = new RedisCacheInvalidationMessage(cacheName, sourceId);
    invalidationTopic.publish(message);
  }
}
