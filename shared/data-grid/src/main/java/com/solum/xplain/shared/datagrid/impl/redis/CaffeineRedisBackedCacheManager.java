package com.solum.xplain.shared.datagrid.impl.redis;

import com.google.common.collect.Sets;
import com.solum.xplain.shared.datagrid.LocalCacheReplica;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import lombok.extern.slf4j.Slf4j;
import org.jspecify.annotations.NullMarked;
import org.jspecify.annotations.Nullable;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.data.redis.cache.RedisCache;
import org.springframework.data.redis.cache.RedisCacheManager;

/**
 * A unified cache manager that combines Spring's RedisCacheManager with write-through caching. It
 * delegates to RedisCacheManager for standard caches and creates write-through caches for caches
 * that have a local cache configuration.
 */
@Slf4j
@NullMarked
public class CaffeineRedisBackedCacheManager implements CacheManager {

  private final Map<String, Cache> caches = new ConcurrentHashMap<>();
  private final RedisCacheManager redisCacheManager;
  private final RedisCacheMessagePublisher publisher;

  public CaffeineRedisBackedCacheManager(
      List<LocalCacheReplica> localCacheReplicas,
      RedisCacheManager redisCacheManager,
      RedisCacheMessagePublisher publisher) {
    this.redisCacheManager = redisCacheManager;
    this.publisher = publisher;

    log.debug("Redis: Initialized custom write-through cache manager");
    initializeCaches(localCacheReplicas);
  }

  private void initializeCaches(List<LocalCacheReplica> localCacheReplicas) {
    for (LocalCacheReplica localCacheReplica : localCacheReplicas) {
      createCache(localCacheReplica);
    }
  }

  /**
   * Returns the cache with the given name, trying the local caches first then the shared caches,
   * null if not found.
   */
  @Override
  @Nullable
  public Cache getCache(String name) {
    Cache cache = caches.get(name);
    if (cache == null) {
      return redisCacheManager.getCache(name);
    }
    return cache;
  }

  /** Returns the names of all caches, both local and shared. */
  @Override
  public Collection<String> getCacheNames() {
    Set<String> localCaches = caches.keySet();
    Set<String> sharedCaches = (Set<String>) redisCacheManager.getCacheNames();
    return Sets.union(localCaches, sharedCaches);
  }

  private void createCache(LocalCacheReplica localCacheReplica) {
    log.info(
        "Creating write-through cache for {} with local config {}",
        localCacheReplica.name(),
        localCacheReplica);

    Cache redisCache = redisCacheManager.getCache(localCacheReplica.name());

    if (redisCache == null) {
      throw new IllegalStateException(
          "Redis cache for " + localCacheReplica.name() + " does not exist");
    }
    caches.put(
        localCacheReplica.name(),
        new CaffeineRedisBackedSpringCache(localCacheReplica, (RedisCache) redisCache, publisher));
  }
}
