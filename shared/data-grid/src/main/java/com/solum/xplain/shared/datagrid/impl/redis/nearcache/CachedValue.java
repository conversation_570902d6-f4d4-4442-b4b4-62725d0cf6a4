package com.solum.xplain.shared.datagrid.impl.redis.nearcache;

import org.jspecify.annotations.NullMarked;
import org.jspecify.annotations.Nullable;
import org.springframework.cache.Cache;
import org.springframework.cache.support.NullValue;
import org.springframework.cache.support.SimpleValueWrapper;

/**
 * Handy representation of a cached value which can adapt into any of the three main forms:
 *
 * <ul>
 *   <li>A wrapped value from a {@link Cache.ValueWrapper}
 *   <li>A raw value that may be null
 *   <li>A stored value that may be a {@link NullValue} or an actual value
 * </ul>
 *
 * @param stored a value that is stored in the cache, which may be a {@link NullValue} or an actual
 *     value
 */
@NullMarked
public record CachedValue(Object stored) {
  public static CachedValue ofWrapped(Cache.ValueWrapper value) {
    return ofRaw(value.get());
  }

  public static CachedValue ofRaw(@Nullable Object value) {
    return new CachedValue(value == null ? NullValue.INSTANCE : value);
  }

  public static CachedValue ofStored(Object storedValue) {
    return new CachedValue(storedValue);
  }

  @Nullable
  public <T> T raw() {
    return rawStored(stored);
  }

  @Nullable
  public <T> T asType(@Nullable Class<T> type) {
    Object value = raw();
    if (value != null && type != null && !type.isInstance(value)) {
      throw new IllegalStateException(
          "Cached value is not of required type [" + type.getName() + "]: " + value);
    }
    return (T) value;
  }

  public Cache.ValueWrapper wrapped() {
    return new SimpleValueWrapper(raw());
  }

  static @Nullable <T> T rawStored(Object stored) {
    return stored == NullValue.INSTANCE ? null : (T) stored;
  }
}
