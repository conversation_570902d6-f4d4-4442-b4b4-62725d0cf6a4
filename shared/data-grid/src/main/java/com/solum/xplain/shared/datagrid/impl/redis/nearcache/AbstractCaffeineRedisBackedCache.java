package com.solum.xplain.shared.datagrid.impl.redis.nearcache;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.solum.xplain.shared.datagrid.LocalCacheReplica;
import com.solum.xplain.shared.datagrid.impl.redis.CaffeineRedisBackedCacheExpiryPolicy;
import com.solum.xplain.shared.datagrid.impl.redis.RedisCacheMessagePublisher;
import java.time.Duration;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.Callable;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import lombok.Getter;
import org.jspecify.annotations.NullMarked;
import org.jspecify.annotations.Nullable;

/**
 * A layer implementing a Caffeine cache that can be used to retrieve values from a local cache on
 * top of an underlying shared cache, which may be a Spring {@link org.springframework.cache.Cache}
 * or a {@link com.solum.xplain.shared.datagrid.KeyValueCache}.
 */
@NullMarked
public abstract class AbstractCaffeineRedisBackedCache {
  @Getter private final String name;
  private final boolean autoUpdate;
  private final Cache<Object, Object> caffeine;
  private final RedisCacheMessagePublisher publisher;
  @Getter private final String sourceId;
  private final CaffeineRedisBackedCacheExpiryPolicy<Object, Object> expiryPolicy;

  public AbstractCaffeineRedisBackedCache(
      LocalCacheReplica config, RedisCacheMessagePublisher publisher) {
    this.name = config.name();
    this.sourceId = config.name() + UUID.randomUUID();
    this.autoUpdate = config.autoUpdate();
    this.publisher = publisher;
    this.expiryPolicy =
        new CaffeineRedisBackedCacheExpiryPolicy<>(config, new ConcurrentHashMap<>());
    this.caffeine =
        Caffeine.newBuilder().maximumSize(config.maxEntries()).expireAfter(expiryPolicy).build();
  }

  @Nullable
  protected CachedValue layeredGet(Object key) {
    Object localStoreValue =
        caffeineGet(
            key,
            k ->
                findInSharedCache(k)
                    .map(CachedValue::stored)
                    // If not found in the shared cache, return null which will prevent caching
                    // locally.
                    .orElse(null));

    return localStoreValue == null // caffeine.get() returns null if not found
        ? null
        : CachedValue.ofStored(localStoreValue);
  }

  protected CachedValue layeredPut(Object key, @Nullable Object value) {
    CachedValue cachedValue = CachedValue.ofRaw(value);
    putValueToSharedCache(key, cachedValue);
    if (autoUpdate) {
      caffeine.put(key, cachedValue.stored());
    } else {
      caffeine.invalidate(key);
    }
    publisher.publishInvalidateKeyMessage(name, key.toString(), sourceId);
    return cachedValue;
  }

  protected CachedValue layeredPut(Object key, @Nullable Object value, Duration ttl) {
    expiryPolicy.addCustomTtl(key, ttl);
    CachedValue cachedValue = layeredPut(key, value);
    setSharedCacheTtl(key, ttl);
    return cachedValue;
  }

  protected CachedValue layeredPutIfAbsent(Object key, Callable<Object> rawValueLoader)
      throws RuntimeException {
    Callable<Object> notifyingLoader =
        () -> {
          publisher.publishInvalidateKeyMessage(name, key.toString(), sourceId);
          return rawValueLoader.call();
        };

    Object storedValue;
    if (autoUpdate) {
      storedValue =
          caffeine.get(key, k -> putValueToSharedCacheIfAbsent(k, notifyingLoader).stored());
    } else {
      storedValue = caffeine.getIfPresent(key);
      if (storedValue == null) {
        storedValue = putValueToSharedCacheIfAbsent(key, notifyingLoader).stored();
      }
    }
    return CachedValue.ofStored(storedValue);
  }

  protected void layeredEvict(Object key) {
    evictFromSharedCache(key);
    evictLocalCacheEntry(key);
    publisher.publishInvalidateKeyMessage(name, key.toString(), sourceId);
  }

  protected void layeredClear() {
    clearSharedCache();
    clearLocalCacheOnly();
    publisher.publishInvalidateAllMessage(name, sourceId);
  }

  @SuppressWarnings("DataFlowIssue") // caffeine.get() is marked @NonNull but can return null
  private @Nullable Object caffeineGet(Object key, Function<Object, @Nullable Object> loader) {
    return caffeine.get(key, loader);
  }

  /** Clear the local cache. */
  public void clearLocalCacheOnly() {
    caffeine.invalidateAll();
  }

  /** Evict a key from local cache only. */
  public void evictLocalCacheEntry(Object key) {
    caffeine.invalidate(key);
  }

  /**
   * Find a value in the shared cache by its key.
   *
   * @param key key to look up in the shared cache
   * @return the value from the shared cache, or empty if not found.
   */
  protected abstract Optional<CachedValue> findInSharedCache(Object key);

  protected abstract void putValueToSharedCache(Object key, CachedValue value);

  protected abstract CachedValue putValueToSharedCacheIfAbsent(
      Object key, Callable<@Nullable Object> rawValueLoader) throws RuntimeException;

  protected abstract void setSharedCacheTtl(Object key, Duration ttl);

  protected abstract void evictFromSharedCache(Object key);

  protected abstract void clearSharedCache();
}
