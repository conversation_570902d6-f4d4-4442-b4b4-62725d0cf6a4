package com.solum.xplain.shared.datagrid.impl.redis;

import com.solum.xplain.shared.datagrid.LocalCacheReplica;
import com.solum.xplain.shared.datagrid.impl.redis.nearcache.AbstractCaffeineRedisBackedCache;
import com.solum.xplain.shared.datagrid.impl.redis.nearcache.CachedValue;
import java.time.Duration;
import java.util.Optional;
import java.util.concurrent.Callable;
import lombok.extern.slf4j.Slf4j;
import org.jspecify.annotations.NullMarked;
import org.jspecify.annotations.Nullable;
import org.springframework.cache.Cache;

/**
 * Cache implementation of a caffeine redis backed Spring cache. This cache writes through to a
 * redis Spring cache backend. Null values are supported.
 *
 * <p>If autoUpdate is true, the cache will update the value when modified and invalidate the key in
 * all other local caches.
 *
 * <p>If autoUpdate is false, the cache will invalidate the key when modified and all other local
 * caches both settings maintain read-your-writes consistency.
 */
@Slf4j
@NullMarked
class CaffeineRedisBackedSpringCache extends AbstractCaffeineRedisBackedCache implements Cache {
  private final Cache sharedCache;

  CaffeineRedisBackedSpringCache(
      LocalCacheReplica config, Cache sharedCache, RedisCacheMessagePublisher publisher) {
    super(config, publisher);
    this.sharedCache = sharedCache;
  }

  @Override
  public Object getNativeCache() {
    return this;
  }

  /**
   * Get the values associated with the given key. If the key is not in the local cache, it will be
   * fetched from the backend store.
   */
  @Override
  @Nullable
  public ValueWrapper get(Object key) {
    CachedValue cachedValue = layeredGet(key);
    return cachedValue == null ? null : cachedValue.wrapped();
  }

  /**
   * Get the value associated with the given key. If the key is not in the local cache, it will be
   * fetched from the backend store.
   */
  @Override
  public @Nullable <T> T get(Object key, @Nullable Class<T> type) {
    CachedValue cachedValue = layeredGet(key);
    return cachedValue == null ? null : cachedValue.asType(type);
  }

  /**
   * Get the values associated with the given key, or compute (and cache) it using the valueLoader.
   * If the key is not in the local cache, it will be fetched from the backend store. If the key is
   * not in the local cache or the backend store, the valueLoader will be used to compute the value.
   * If the valueLoader returns null it will return null.
   */
  @Override
  @SuppressWarnings("unchecked")
  public @Nullable <T> T get(Object key, Callable<@Nullable T> valueLoader) {
    try {
      CachedValue cachedValue = layeredPutIfAbsent(key, (Callable<Object>) valueLoader);
      return cachedValue.raw();
    } catch (ValueRetrievalException e) {
      throw e; // rethrow the exception as is
    } catch (Exception e) {
      throw new ValueRetrievalException(key, valueLoader, e);
    }
  }

  /**
   * Put a value in both the local and backend cache. If autoUpdate is true, the local cache will be
   * updated with the new value. If autoUpdate is false, the local cache will be invalidated. And
   * the next get will miss the local cache and hit the backend store.
   */
  @Override
  public void put(Object key, @Nullable Object value) {
    layeredPut(key, value);
  }

  /** Evict a key from both the local and backend cache. */
  @Override
  public void evict(Object key) {
    layeredEvict(key);
  }

  /** Clear the shared and local cache. */
  @Override
  public void clear() {
    layeredClear();
  }

  @Override
  protected Optional<CachedValue> findInSharedCache(Object key) {
    return Optional.ofNullable(sharedCache.get(key)).map(CachedValue::ofWrapped);
  }

  @Override
  protected void putValueToSharedCache(Object key, CachedValue value) {
    sharedCache.put(key, value.raw());
  }

  @Override
  protected CachedValue putValueToSharedCacheIfAbsent(
      Object key, Callable<@Nullable Object> rawValueLoader) {
    return CachedValue.ofRaw(sharedCache.get(key, rawValueLoader));
  }

  @Override
  protected void setSharedCacheTtl(Object key, Duration ttl) {
    throw new UnsupportedOperationException("Spring cache does not support per-entry TTLs");
  }

  @Override
  protected void evictFromSharedCache(Object key) {
    sharedCache.evict(key);
  }

  @Override
  protected void clearSharedCache() {
    sharedCache.clear();
  }
}
