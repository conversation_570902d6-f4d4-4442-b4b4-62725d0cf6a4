package com.solum.xplain.shared.utils.cache

import com.solum.xplain.core.common.ScrollRequest
import com.solum.xplain.core.common.ScrollableEntry
import com.solum.xplain.core.error.Error
import com.solum.xplain.core.error.ErrorItem
import io.atlassian.fugue.Either
import org.aspectj.lang.ProceedingJoinPoint
import org.springframework.data.domain.Sort
import spock.lang.Specification

class FetchDelegateTest extends Specification {

  def "forScrollableEntry should create ScrollRequestFetchDelegate"() {
    given:
    def pjp = Mock(ProceedingJoinPoint)
    def scrollRequest = ScrollRequest.of(0, 10, Sort.by("id"))
    pjp.getArgs() >> [scrollRequest, "otherParam"]

    when:
    def delegate = FetchDelegate.forScrollableEntry(pjp)

    then:
    delegate instanceof FetchDelegate.ScrollRequestFetchDelegate
    delegate.class.simpleName == "ScrollRequestFetchDelegate"
  }

  def "forScrollableEntryEither should create EitherFetchDelegate"() {
    given:
    def pjp = Mock(ProceedingJoinPoint)
    def scrollRequest = ScrollRequest.of(0, 10, Sort.by("id"))
    pjp.getArgs() >> [scrollRequest, "otherParam"]

    when:
    def delegate = FetchDelegate.forScrollableEntryEither(pjp)

    then:
    delegate instanceof FetchDelegate.EitherFetchDelegate
    delegate.class.simpleName == "EitherFetchDelegate"
  }

  def "ScrollRequestFetchDelegate should find and replace ScrollRequest parameter"() {
    given:
    def pjp = Mock(ProceedingJoinPoint)
    def originalScrollRequest = ScrollRequest.of(10, 5, Sort.by("name"))
    def newScrollRequest = ScrollRequest.of(0, 0, Sort.by("name"))
    def data = [new TestData(id: "1")]
    def result = ScrollableEntry.of(data, newScrollRequest)

    pjp.getArgs() >> ["param1", originalScrollRequest, "param3"]
    pjp.proceed(_) >> result

    def delegate = FetchDelegate.forScrollableEntry(pjp)

    when:
    def fetchResult = delegate.fetch(newScrollRequest)

    then:
    fetchResult == result
    1 * pjp.proceed(["param1", newScrollRequest, "param3"]) >> result
  }

  def "EitherFetchDelegate should find and replace ScrollRequest parameter"() {
    given:
    def pjp = Mock(ProceedingJoinPoint)
    def originalScrollRequest = ScrollRequest.of(10, 5, Sort.by("name"))
    def newScrollRequest = ScrollRequest.of(0, 0, Sort.by("name"))
    def data = [new TestData(id: "1")]
    def result = Either.right(ScrollableEntry.of(data, newScrollRequest))

    pjp.getArgs() >> ["param1", originalScrollRequest, "param3"]
    pjp.proceed(_) >> result

    def delegate = FetchDelegate.forScrollableEntryEither(pjp)

    when:
    def fetchResult = delegate.fetch(newScrollRequest)

    then:
    fetchResult == result
    1 * pjp.proceed(["param1", newScrollRequest, "param3"]) >> result
  }

  def "should throw IllegalArgumentException when no ScrollRequest found in parameters"() {
    given:
    def pjp = Mock(ProceedingJoinPoint)
    pjp.getArgs() >> ["param1", "param2", 123]

    def delegate = FetchDelegate.forScrollableEntry(pjp)

    when:
    delegate.fetch(ScrollRequest.of(0, 10, Sort.by("id")))

    then:
    def exception = thrown(IllegalArgumentException)
    exception.message == "No ScrollRequest found in parameters"
  }

  def "should handle multiple ScrollRequest parameters by using the first one"() {
    given:
    def pjp = Mock(ProceedingJoinPoint)
    def firstScrollRequest = ScrollRequest.of(5, 3, Sort.by("date"))
    def secondScrollRequest = ScrollRequest.of(10, 7, Sort.by("name"))
    def newScrollRequest = ScrollRequest.of(0, 0, Sort.by("date"))
    def data = [new TestData(id: "1")]
    def result = ScrollableEntry.of(data, newScrollRequest)

    pjp.getArgs() >> [firstScrollRequest, "param", secondScrollRequest]
    pjp.proceed(_) >> result

    def delegate = FetchDelegate.forScrollableEntry(pjp)

    when:
    delegate.fetch(newScrollRequest)

    then:
    // Should replace the first ScrollRequest (index 0), not the second
    1 * pjp.proceed([newScrollRequest, "param", secondScrollRequest])
  }

  def "should handle exceptions from ProceedingJoinPoint"() {
    given:
    def pjp = Mock(ProceedingJoinPoint)
    def scrollRequest = ScrollRequest.of(0, 10, Sort.by("id"))

    pjp.getArgs() >> [scrollRequest]
    pjp.proceed(_) >> { throw new RuntimeException("Method execution failed") }

    def delegate = FetchDelegate.forScrollableEntry(pjp)

    when:
    delegate.fetch(ScrollRequest.of(0, 0, Sort.by("id")))

    then:
    def exception = thrown(RuntimeException)
    exception.message == "Method execution failed"
  }

  def "EitherFetchDelegate should handle error results"() {
    given:
    def pjp = Mock(ProceedingJoinPoint)
    def scrollRequest = ScrollRequest.of(0, 10, Sort.by("id"))
    def error = new ErrorItem(Error.OBJECT_NOT_FOUND, "Data not found")
    def result = Either.left(error)

    pjp.getArgs() >> [scrollRequest]
    pjp.proceed(_) >> result

    def delegate = FetchDelegate.forScrollableEntryEither(pjp)

    when:
    def fetchResult = delegate.fetch(ScrollRequest.of(0, 0, Sort.by("id")))

    then:
    fetchResult.isLeft()
    fetchResult.left().get() == error
  }

  static class TestData {
    String id
  }
}
