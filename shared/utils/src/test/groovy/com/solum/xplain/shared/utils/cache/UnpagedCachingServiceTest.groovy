package com.solum.xplain.shared.utils.cache

import com.solum.xplain.core.common.ScrollRequest
import com.solum.xplain.core.common.ScrollableEntry
import com.solum.xplain.core.error.Error
import com.solum.xplain.core.error.ErrorItem
import io.atlassian.fugue.Either
import org.springframework.data.domain.Sort
import spock.lang.Specification

class UnpagedCachingServiceTest extends Specification {
  private final eventHandler = Mock(UnpagedCacheEventHandler)
  static class DataView {
    String id
  }

  static class TestEvent {
    String type
  }

  def "should call provided function with scroll request containing sort only"() {
    given:
    def service = new UnpagedCachingService(eventHandler)
    def data = [new DataView(id: "1"), new DataView(id: "2")]
    def function = Mock(UnpagedCachingService.FetchWithScrollRequest)

    when:
    def result = service.cacheUnpaged("test1", "user1", function, [] as Class<?>[], ScrollRequest.of(0, 1, Sort.by("id")))
    def result2 = service.cacheUnpaged("test1", "user1", function, [] as Class<?>[], ScrollRequest.of(1, 2, Sort.by("id")))

    then:
    result.content*.id == ["1"]
    result2.content*.id == ["2"]
    result.startRow == 0
    result.endRow == 1
    result.lastRow == 2
    result2.startRow == 1
    result2.endRow == 2
    result2.lastRow == 2

    and: "we only called the function once to fetch the unpaged data"
    1 * function.fetch({ it instanceof ScrollRequest && it.sort == Sort.by("id") }) >> { ScrollRequest req ->
      return ScrollableEntry.of(data, req)
    }

    and: "event handler was called to register event mapping"
    2 * eventHandler.registerEventMapping("test1", [] as Class<?>[])
  }

  def "should use separate caches for different cacheIds"() {
    given:
    def service = new UnpagedCachingService(eventHandler)
    def data = [new DataView(id: "1"), new DataView(id: "2")]
    def function = Mock(UnpagedCachingService.FetchWithScrollRequest)

    when:
    def result = service.cacheUnpaged("test1", "user1", function, [] as Class<?>[], ScrollRequest.of(0, 1, Sort.by("id")))
    def result2 = service.cacheUnpaged(2L, "user1", function, [] as Class<?>[], ScrollRequest.of(1, 2, Sort.by("id")))

    then:
    result.content*.id == ["1"]
    result2.content*.id == ["2"]
    result.startRow == 0
    result.endRow == 1
    result.lastRow == 2
    result2.startRow == 1
    result2.endRow == 2
    result2.lastRow == 2

    and: "we called the function twice because the cache IDs were different"
    2 * function.fetch({ it instanceof ScrollRequest && it.sort == Sort.by("id") }) >> { ScrollRequest req ->
      return ScrollableEntry.of(data, req)
    }
  }

  def "should cache separately for different users"() {
    given:
    def service = new UnpagedCachingService(eventHandler)
    def data = [new DataView(id: "1"), new DataView(id: "2")]
    def function = Mock(UnpagedCachingService.FetchWithScrollRequest)

    when:
    def result = service.cacheUnpaged("test1", "user1", function, [] as Class<?>[], ScrollRequest.of(0, 1, Sort.by("id")))
    def result2 = service.cacheUnpaged("test1", "user2", function, [] as Class<?>[], ScrollRequest.of(1, 2, Sort.by("id")))

    then:
    result.content*.id == ["1"]
    result2.content*.id == ["2"]
    result.startRow == 0
    result.endRow == 1
    result.lastRow == 2
    result2.startRow == 1
    result2.endRow == 2
    result2.lastRow == 2

    and: "we called the function twice because the cache IDs were different"
    2 * function.fetch({ it instanceof ScrollRequest && it.sort == Sort.by("id") }) >> { ScrollRequest req ->
      return ScrollableEntry.of(data, req)
    }
  }

  def "should register event mappings when invalidateOnEvent is provided"() {
    given:
    def service = new UnpagedCachingService(eventHandler)
    def data = [new DataView(id: "1")]
    def function = Mock(UnpagedCachingService.FetchWithScrollRequest)
    def eventClasses = [TestEvent] as Class<?>[]

    when:
    service.cacheUnpaged("test1", "user1", function, eventClasses, ScrollRequest.of(0, 1, Sort.by("id")))

    then:
    1 * eventHandler.registerEventMapping("test1", eventClasses)
    1 * function.fetch(_) >> ScrollableEntry.of(data, ScrollRequest.of(0, 0, Sort.by("id")))
  }

  def "should invalidate specific cache when UnpagedCacheInvalidationEvent is received"() {
    given:
    def service = new UnpagedCachingService(eventHandler)
    def data = [new DataView(id: "1")]
    def function = Mock(UnpagedCachingService.FetchWithScrollRequest)

    when: "cache some data"
    service.cacheUnpaged("test1", "user1", function, [] as Class<?>[], ScrollRequest.of(0, 1, Sort.by("id")))

    and: "invalidate the cache via event"
    service.invalidateCache(new UnpagedCacheInvalidationEvent("test1"))

    and: "fetch again"
    service.cacheUnpaged("test1", "user1", function, [] as Class<?>[], ScrollRequest.of(0, 1, Sort.by("id")))

    then: "function should be called twice since cache was invalidated"
    2 * function.fetch(_) >> ScrollableEntry.of(data, ScrollRequest.of(0, 0, Sort.by("id")))
  }

  def "cacheUnpagedEither should work with successful Either results"() {
    given:
    def service = new UnpagedCachingService(eventHandler)
    def data = [new DataView(id: "1"), new DataView(id: "2")]
    def function = Mock(UnpagedCachingService.FetchWithScrollRequestEither)

    when:
    def result = service.cacheUnpagedEither("test1", "user1", function, [] as Class<?>[], ScrollRequest.of(0, 1, Sort.by("id")))
    def result2 = service.cacheUnpagedEither("test1", "user1", function, [] as Class<?>[], ScrollRequest.of(1, 2, Sort.by("id")))

    then:
    result.isRight()
    result2.isRight()
    with (result.right().get()) {
      content*.id == ["1"]
      startRow == 0
      endRow == 1
      lastRow == 2
    }
    with (result2.right().get()) {
      content*.id == ["2"]
      startRow == 1
      endRow == 2
      lastRow == 2
    }

    and: "we only called the function once to fetch the unpaged data"
    1 * function.fetch({ it instanceof ScrollRequest && it.sort == Sort.by("id") }) >> { ScrollRequest req ->
      return Either.right(ScrollableEntry.of(data, req))
    }

    and: "event handler was called to register event mapping"
    2 * eventHandler.registerEventMapping("test1", [] as Class<?>[])
  }

  def "cacheUnpagedEither should handle error results without caching"() {
    given:
    def service = new UnpagedCachingService(eventHandler)
    def error = new ErrorItem(Error.OBJECT_NOT_FOUND, "Data not found")
    def function = Mock(UnpagedCachingService.FetchWithScrollRequestEither)

    when:
    def result = service.cacheUnpagedEither("test1", "user1", function, [] as Class<?>[], ScrollRequest.of(0, 1, Sort.by("id")))
    def result2 = service.cacheUnpagedEither("test1", "user1", function, [] as Class<?>[], ScrollRequest.of(1, 2, Sort.by("id")))

    then:
    result.isLeft()
    result2.isLeft()
    verifyEach ([result.left().get(), result2.left().get()]) {
      it.reason == error.reason
      it.description == error.description
    }

    and: "function should be called twice unpaged and twice with the original scroll request since errors are not cached"
    2 * function.fetch(ScrollRequest.of(0, 0, Sort.by("id"))) >> Either.left(error)
    1 * function.fetch(ScrollRequest.of(0, 1, Sort.by("id"))) >> Either.left(error)
    1 * function.fetch(ScrollRequest.of(1, 2, Sort.by("id"))) >> Either.left(error)
  }

  def "FetchWithScrollRequestEither.wrap should convert FetchWithScrollRequest to Either version"() {
    given:
    def data = [new DataView(id: "1")]
    def originalFunction = Mock(UnpagedCachingService.FetchWithScrollRequest)
    def wrappedFunction = UnpagedCachingService.FetchWithScrollRequestEither.wrap(originalFunction)
    def scrollRequest = ScrollRequest.of(0, 1, Sort.by("id"))

    when:
    def result = wrappedFunction.fetch(scrollRequest)

    then:
    result.isRight()
    result.right().get().content*.id == ["1"]
    1 * originalFunction.fetch(scrollRequest) >> ScrollableEntry.of(data, scrollRequest)
  }

  def "FetchWithScrollRequestEither.wrap should propagate exceptions as runtime exceptions"() {
    given:
    def originalFunction = Mock(UnpagedCachingService.FetchWithScrollRequest)
    def wrappedFunction = UnpagedCachingService.FetchWithScrollRequestEither.wrap(originalFunction)
    def scrollRequest = ScrollRequest.of(0, 1, Sort.by("id"))

    when:
    wrappedFunction.fetch(scrollRequest)

    then:
    def exception = thrown(RuntimeException)
    exception.message == "Original error"
    1 * originalFunction.fetch(scrollRequest) >> { throw new RuntimeException("Original error") }
  }

  def "cacheUnpaged should delegate to cacheUnpagedEither"() {
    given:
    def service = new UnpagedCachingService(eventHandler)
    def data = [new DataView(id: "1"), new DataView(id: "2")]
    def function = Mock(UnpagedCachingService.FetchWithScrollRequest)

    when:
    def result = service.cacheUnpaged("test1", "user1", function, [] as Class<?>[], ScrollRequest.of(0, 1, Sort.by("id")))

    then:
    result.content*.id == ["1"]
    result.startRow == 0
    result.endRow == 1
    result.lastRow == 2

    and: "the original function is called through the Either wrapper"
    1 * function.fetch({ it instanceof ScrollRequest && it.sort == Sort.by("id") }) >> { ScrollRequest req ->
      return ScrollableEntry.of(data, req)
    }

    and: "event handler was called to register event mapping"
    1 * eventHandler.registerEventMapping("test1", [] as Class<?>[])
  }
}
