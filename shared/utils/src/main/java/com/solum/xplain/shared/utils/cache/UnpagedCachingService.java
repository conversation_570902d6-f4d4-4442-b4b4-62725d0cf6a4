package com.solum.xplain.shared.utils.cache;

import static com.solum.xplain.shared.utils.cache.UnpagedCachingService.FetchWithScrollRequestEither.wrap;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.util.concurrent.UncheckedExecutionException;
import com.solum.xplain.core.authentication.value.XplainPrincipal;
import com.solum.xplain.core.common.ScrollRequest;
import com.solum.xplain.core.common.ScrollableEntry;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.shared.utils.event.CacheInvalidationListener;
import io.atlassian.fugue.Either;
import java.io.Serializable;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.NullMarked;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Slf4j
@Service
@NullMarked
public class UnpagedCachingService implements CacheInvalidationListener {
  private final LoadingCache<Serializable, Cache<UnpagedCacheKey, ScrollableEntry<?>>> cachecache =
      CacheBuilder.newBuilder()
          .expireAfterAccess(5, TimeUnit.MINUTES)
          .build(
              CacheLoader.from(
                  () -> CacheBuilder.newBuilder().expireAfterAccess(30, TimeUnit.SECONDS).build()));

  private final UnpagedCacheEventHandler eventHandler;

  @FunctionalInterface
  public interface FetchWithScrollRequest<T> {
    ScrollableEntry<T> fetch(ScrollRequest scrollRequest);
  }

  @FunctionalInterface
  public interface FetchWithScrollRequestEither<T> {
    Either<ErrorItem, ScrollableEntry<T>> fetch(ScrollRequest scrollRequest);

    static <T> FetchWithScrollRequestEither<T> wrap(FetchWithScrollRequest<T> fetchFunction) {
      return scrollRequest -> Either.right(fetchFunction.fetch(scrollRequest));
    }
  }

  public <T> ScrollableEntry<T> cacheUnpaged(
      Serializable cacheId,
      Object principal,
      FetchWithScrollRequest<T> fetchFunction,
      Class<?>[] invalidateOnEvent,
      Object... params) {

    Either<ErrorItem, ScrollableEntry<T>> result =
        cacheUnpagedEither(cacheId, principal, wrap(fetchFunction), invalidateOnEvent, params);

    return result.getOrError(() -> "Unexpected error in cacheUnpaged: " + result.left().get());
  }

  public <T> Either<ErrorItem, ScrollableEntry<T>> cacheUnpagedEither(
      Serializable cacheId,
      Object principal,
      FetchWithScrollRequestEither<T> fetchFunction,
      Class<?>[] invalidateOnEvent,
      Object... params) {

    // Register event mappings for this cache
    eventHandler.registerEventMapping(cacheId, invalidateOnEvent);

    ScrollRequest originalScrollRequest = getOriginalScrollRequest(params);
    Either<ErrorItem, ScrollableEntry<T>> unpagedResults;
    UnpagedCacheKey key = getKey(principal, params);
    try {
      Cache<UnpagedCacheKey, ScrollableEntry<T>> cache = getCache(cacheId);

      ScrollableEntry<T> cachedResult =
          cache.get(
              key,
              () -> {
                log.trace("Cache miss for unpaged request with key: {}, fetching unpaged", key);
                ScrollRequest unpagedScrollRequest =
                    ScrollRequest.of(0L, 0L, originalScrollRequest.getSort());
                Either<ErrorItem, ScrollableEntry<T>> eitherResult =
                    fetchFunction.fetch(unpagedScrollRequest);

                // Only cache successful results
                if (eitherResult.isRight()) {
                  ScrollableEntry<T> unpagedResult = eitherResult.right().get();
                  log.trace(
                      "Unpaged request fetched with {} entries", unpagedResult.getContent().size());
                  return unpagedResult;
                } else {
                  // Don't cache errors, throw exception to prevent caching
                  throw new RuntimeException(
                      "Failed to fetch unpaged data: " + eitherResult.left().get());
                }
              });

      unpagedResults = Either.right(cachedResult);
    } catch (ExecutionException | UncheckedExecutionException e) {
      log.warn("Error fetching unpaged results for key: {}, error: {}", key, e.getMessage());
      // If there's an error fetching the unpaged results, fall back to the original scroll request.
      return fetchFunction.fetch(originalScrollRequest);
    }

    // Now chop those down to the page requested
    return unpagedResults.map(
        unpagedResult -> {
          ScrollRequest scrollRequest = getOriginalScrollRequest(params);
          int pageSize = scrollRequest.getPageSize();
          List<T> content = unpagedResult.getContent();
          int start = (int) scrollRequest.getOffset();
          int end = Math.min(start + pageSize, content.size());
          log.debug(
              "Returning cached paged content from {} to {}, total size: {} for {}",
              start,
              end,
              content.size(),
              key);
          List<T> pagedContent = content.subList(start, end);
          return ScrollableEntry.of(
              pagedContent,
              scrollRequest,
              unpagedResult.getLastRow() == null
                  ? (long) content.size()
                  : unpagedResult.getLastRow());
        });
  }

  @SuppressWarnings({"unchecked", "rawtypes"})
  private <T> Cache<UnpagedCacheKey, ScrollableEntry<T>> getCache(Serializable cacheId)
      throws ExecutionException {
    return (Cache<UnpagedCacheKey, ScrollableEntry<T>>) (Cache) cachecache.get(cacheId);
  }

  private @NonNull UnpagedCacheKey getKey(Object principal, Object[] params) {
    Object[] keyParams = new Object[params.length + 1];
    int index = 0;
    for (Object param : params) {
      if (param instanceof UnpagedCacheAware alternate) {
        keyParams[index++] = alternate.getUnpagedCacheKey();
      } else {
        keyParams[index++] = param;
      }
    }
    keyParams[index++] =
        principal instanceof XplainPrincipal xpl && xpl.getId() != null ? xpl.getId() : principal;
    return new UnpagedCacheKey(keyParams);
  }

  private ScrollRequest getOriginalScrollRequest(Object[] params) {
    for (Object param : params) {
      if (param instanceof ScrollRequest scrollRequest) {
        return scrollRequest;
      }
    }
    throw new IllegalArgumentException("No ScrollRequest found in parameters");
  }

  @Override
  public void invalidateAll() {
    cachecache.asMap().values().forEach(Cache::invalidateAll);
    cachecache.invalidateAll();
  }

  @EventListener(UnpagedCacheInvalidationEvent.class)
  public void invalidateCache(UnpagedCacheInvalidationEvent event) {
    Serializable cacheId = event.cacheId();
    try {
      Cache<UnpagedCacheKey, ScrollableEntry<?>> cache = cachecache.get(cacheId);
      cache.invalidateAll();
    } catch (ExecutionException | UncheckedExecutionException e) {
      log.warn("Error invalidating cache '{}': {}", cacheId, e.getMessage());
    }
  }

  private record UnpagedCacheKey(Object[] params) {
    @Override
    public boolean equals(Object obj) {
      if (!(obj instanceof UnpagedCacheKey cacheKey)) return false;
      return Arrays.equals(params, cacheKey.params);
    }

    @Override
    public int hashCode() {
      return Arrays.hashCode(params);
    }

    @Override
    public @NonNull String toString() {
      return Arrays.toString(params);
    }
  }
}
