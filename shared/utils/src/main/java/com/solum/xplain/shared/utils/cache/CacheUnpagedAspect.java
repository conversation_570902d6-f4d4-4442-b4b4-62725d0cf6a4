package com.solum.xplain.shared.utils.cache;

import com.solum.xplain.core.common.ScrollRequest;
import lombok.RequiredArgsConstructor;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.jspecify.annotations.NullMarked;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

/**
 * This aspect intercepts methods annotated with @CacheUnpaged and uses {@link
 * UnpagedCachingService} to cache the results of the method calls.
 *
 * <p>If the cache needs loading, {@code UnpagedCachingService} provides an unpaged {@link
 * ScrollRequest} to the {@link FetchDelegate.ScrollRequestFetchDelegate}. This then calls the
 * original method with the unpaged version of the {@code ScrolLRequest} as a parameter in place of
 * the originally supplied one.
 */
@RequiredArgsConstructor
@Aspect
@Component
@NullMarked
public class CacheUnpagedAspect {
  private final UnpagedCachingService unpagedCachingService;

  @Pointcut("execution(com.solum.xplain.core.common.ScrollableEntry+ *(..))")
  private void returnsScrollableEntry() {}

  @Pointcut(
      "execution(io.atlassian.fugue.Either<com.solum.xplain.core.error.ErrorItem, com.solum.xplain.core.common.ScrollableEntry+> *(..))")
  private void returnsEitherScrollableEntry() {}

  @Around("@annotation(cacheUnpaged) && returnsScrollableEntry()")
  public Object cacheUnpagedScrollableEntry(ProceedingJoinPoint pjp, CacheUnpaged cacheUnpaged) {
    return unpagedCachingService.cacheUnpaged(
        pjp.getSignature().toString(),
        SecurityContextHolder.getContext().getAuthentication().getPrincipal(),
        FetchDelegate.forScrollableEntry(pjp),
        cacheUnpaged.invalidateOnEvent(),
        pjp.getArgs());
  }

  @Around("returnsEitherScrollableEntry() && @annotation(cacheUnpaged)")
  public Object cacheUnpagedScrollableEntryEither(
      ProceedingJoinPoint pjp, CacheUnpaged cacheUnpaged) {
    return unpagedCachingService.cacheUnpagedEither(
        pjp.getSignature().toString(),
        SecurityContextHolder.getContext().getAuthentication().getPrincipal(),
        FetchDelegate.forScrollableEntryEither(pjp),
        cacheUnpaged.invalidateOnEvent(),
        pjp.getArgs());
  }
}
