package com.solum.xplain.extensions.product;

import static com.opengamma.strata.basics.currency.Currency.CHF;
import static com.opengamma.strata.basics.currency.Currency.EUR;
import static com.opengamma.strata.basics.currency.Currency.GBP;
import static com.opengamma.strata.basics.currency.Currency.JPY;
import static com.opengamma.strata.basics.currency.Currency.USD;
import static com.opengamma.strata.basics.date.BusinessDayConventions.MODIFIED_FOLLOWING;
import static com.opengamma.strata.basics.date.DayCounts.ONE_ONE;
import static com.opengamma.strata.basics.date.DayCounts.THIRTY_E_360;
import static com.opengamma.strata.basics.date.HolidayCalendarIds.CHZU;
import static com.opengamma.strata.basics.date.HolidayCalendarIds.EUTA;
import static com.opengamma.strata.basics.date.HolidayCalendarIds.FRPA;
import static com.opengamma.strata.basics.date.HolidayCalendarIds.GBLO;
import static com.opengamma.strata.basics.date.HolidayCalendarIds.JPTO;
import static com.solum.xplain.extensions.calendar.ExtendedHolidayCalendarIds.GBLO_JPTO;
import static com.solum.xplain.extensions.calendar.ExtendedHolidayCalendarIds.GBLO_USNY;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.date.BusinessDayAdjustment;
import com.opengamma.strata.basics.date.DayCount;
import com.opengamma.strata.basics.date.DayCounts;
import com.opengamma.strata.basics.date.DaysAdjustment;
import com.opengamma.strata.basics.date.HolidayCalendarId;
import com.opengamma.strata.basics.index.PriceIndex;
import com.opengamma.strata.basics.index.PriceIndices;
import com.opengamma.strata.basics.schedule.Frequency;
import com.opengamma.strata.product.swap.CompoundingMethod;
import com.opengamma.strata.product.swap.PriceIndexCalculationMethod;
import com.opengamma.strata.product.swap.type.FixedInflationSwapConvention;
import com.opengamma.strata.product.swap.type.FixedRateSwapLegConvention;
import com.opengamma.strata.product.swap.type.ImmutableFixedInflationSwapConvention;
import com.opengamma.strata.product.swap.type.InflationRateSwapLegConvention;
import com.solum.xplain.extensions.calendar.ExtendedHolidayCalendarIds;
import com.solum.xplain.extensions.constants.PriceIndexConstants;
import com.solum.xplain.extensions.index.ExtendedPriceIndices;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@SuppressWarnings("unused")
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ExtendedFixedInflationSwapConventions {

  public static final FixedInflationSwapConvention GBP_FIXED_ZC_GB_HICP =
      makeConvention(
          "GBP-FIXED-ZC-GB-HICP",
          GBP,
          GBLO,
          PriceIndices.GB_HICP,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, GBLO),
          PriceIndexCalculationMethod.MONTHLY,
          0,
          ONE_ONE,
          CompoundingMethod.STRAIGHT);

  public static final FixedInflationSwapConvention GBP_FIXED_ZC_GB_HICP_30_360 =
      makeConvention(
          "GBP-FIXED-ZC-GB-HICP-30360",
          GBP,
          GBLO,
          PriceIndices.GB_HICP,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, GBLO),
          PriceIndexCalculationMethod.MONTHLY,
          0,
          THIRTY_E_360,
          CompoundingMethod.STRAIGHT);

  public static final FixedInflationSwapConvention GBP_FIXED_ZC_GB_RPI =
      makeConvention(
          "GBP-FIXED-ZC-GB-RPI",
          GBP,
          GBLO,
          PriceIndices.GB_RPI,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, GBLO),
          PriceIndexCalculationMethod.MONTHLY,
          0,
          ONE_ONE,
          CompoundingMethod.STRAIGHT);

  public static final FixedInflationSwapConvention GBP_FIXED_ZC_GB_RPI_30_360 =
      makeConvention(
          "GBP-FIXED-ZC-GB-RPI-30360",
          GBP,
          GBLO,
          PriceIndices.GB_RPI,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, GBLO),
          PriceIndexCalculationMethod.MONTHLY,
          0,
          THIRTY_E_360,
          CompoundingMethod.STRAIGHT);

  public static final FixedInflationSwapConvention GBP_FIXED_ZC_GB_RPI_CLEARED =
      makeClearedConvention(
          "GBP-FIXED-ZC-GB-RPI-CLEARED",
          GBP,
          GBLO,
          PriceIndices.GB_RPI,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, GBLO),
          0,
          CompoundingMethod.STRAIGHT);

  public static final FixedInflationSwapConvention GBP_FIXED_ZC_GB_RPI_CLEARED_30_360 =
      makeConvention(
          "GBP-FIXED-ZC-GB-RPI-CLEARED-30360",
          GBP,
          GBLO,
          PriceIndices.GB_RPI,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, GBLO),
          PriceIndexCalculationMethod.MONTHLY,
          0,
          THIRTY_E_360,
          CompoundingMethod.STRAIGHT);

  public static final FixedInflationSwapConvention GBP_FIXED_ZC_GB_CPI_CLEARED =
      makeClearedConvention(
          "GBP-FIXED-ZC-GB-CPI-CLEARED",
          GBP,
          GBLO,
          ExtendedPriceIndices.GB_CPI,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, GBLO),
          0,
          CompoundingMethod.FLAT);

  public static final FixedInflationSwapConvention GBP_FIXED_ZC_GB_CPI_CLEARED_30_360 =
      makeConvention(
          "GBP-FIXED-ZC-GB-CPI-CLEARED-30360",
          GBP,
          GBLO,
          ExtendedPriceIndices.GB_CPI,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, GBLO),
          PriceIndexCalculationMethod.MONTHLY,
          0,
          THIRTY_E_360,
          CompoundingMethod.FLAT);

  public static final FixedInflationSwapConvention GBP_FIXED_ZC_GB_CPI =
      makeConvention(
          "GBP-FIXED-ZC-GB-CPI",
          GBP,
          GBLO,
          ExtendedPriceIndices.GB_CPI,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, GBLO),
          PriceIndexCalculationMethod.MONTHLY,
          0,
          ONE_ONE,
          CompoundingMethod.FLAT);

  public static final FixedInflationSwapConvention GBP_FIXED_ZC_GB_CPI_30_360 =
      makeConvention(
          "GBP-FIXED-ZC-GB-CPI-30360",
          GBP,
          GBLO,
          ExtendedPriceIndices.GB_CPI,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, GBLO),
          PriceIndexCalculationMethod.MONTHLY,
          0,
          THIRTY_E_360,
          CompoundingMethod.FLAT);

  public static final FixedInflationSwapConvention GBP_FIXED_ZC_GB_RPIX =
      makeConvention(
          "GBP-FIXED-ZC-GB-RPIX",
          GBP,
          GBLO,
          PriceIndices.GB_RPIX,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, GBLO),
          PriceIndexCalculationMethod.MONTHLY,
          0,
          ONE_ONE,
          CompoundingMethod.STRAIGHT);

  public static final FixedInflationSwapConvention GBP_FIXED_ZC_GB_RPIX_30_360 =
      makeConvention(
          "GBP-FIXED-ZC-GB-RPIX-30360",
          GBP,
          GBLO,
          PriceIndices.GB_RPIX,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, GBLO),
          PriceIndexCalculationMethod.MONTHLY,
          0,
          THIRTY_E_360,
          CompoundingMethod.STRAIGHT);

  public static final FixedInflationSwapConvention CHF_FIXED_ZC_CH_CPI =
      makeConvention(
          "CHF-FIXED-ZC-CH-CPI",
          CHF,
          CHZU,
          PriceIndices.CH_CPI,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, CHZU),
          PriceIndexCalculationMethod.MONTHLY,
          2,
          ONE_ONE,
          CompoundingMethod.STRAIGHT);

  public static final FixedInflationSwapConvention CHF_FIXED_ZC_CH_CPI_30_360 =
      makeConvention(
          "CHF-FIXED-ZC-CH-CPI-30360",
          CHF,
          CHZU,
          PriceIndices.CH_CPI,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, CHZU),
          PriceIndexCalculationMethod.MONTHLY,
          2,
          THIRTY_E_360,
          CompoundingMethod.STRAIGHT);

  public static final FixedInflationSwapConvention EUR_FIXED_ZC_EU_AI_CPI =
      makeConvention(
          "EUR-FIXED-ZC-EU-AI-CPI",
          EUR,
          EUTA,
          PriceIndices.EU_AI_CPI,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, EUTA),
          PriceIndexCalculationMethod.MONTHLY,
          2,
          ONE_ONE,
          CompoundingMethod.STRAIGHT);

  public static final FixedInflationSwapConvention EUR_FIXED_ZC_EU_AI_CPI_30_360 =
      makeConvention(
          "EUR-FIXED-ZC-EU-AI-CPI-30360",
          EUR,
          EUTA,
          PriceIndices.EU_AI_CPI,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, EUTA),
          PriceIndexCalculationMethod.MONTHLY,
          2,
          THIRTY_E_360,
          CompoundingMethod.STRAIGHT);

  public static final FixedInflationSwapConvention EUR_FIXED_ZC_EU_EXT_CPI =
      makeConvention(
          "EUR-FIXED-ZC-EU-EXT-CPI",
          EUR,
          EUTA,
          PriceIndices.EU_EXT_CPI,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, EUTA),
          PriceIndexCalculationMethod.MONTHLY,
          2,
          ONE_ONE,
          CompoundingMethod.STRAIGHT);

  public static final FixedInflationSwapConvention EUR_FIXED_ZC_EU_EXT_CPI_30_360 =
      makeConvention(
          "EUR-FIXED-ZC-EU-EXT-CPI-30360",
          EUR,
          EUTA,
          PriceIndices.EU_EXT_CPI,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, EUTA),
          PriceIndexCalculationMethod.MONTHLY,
          2,
          THIRTY_E_360,
          CompoundingMethod.STRAIGHT);

  public static final FixedInflationSwapConvention EUR_FIXED_ZC_EU_EXT_CPI_CLEARED =
      makeClearedConvention(
          "EUR-FIXED-ZC-EU-EXT-CPI-CLEARED",
          EUR,
          EUTA,
          PriceIndices.EU_EXT_CPI,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, EUTA),
          2,
          CompoundingMethod.STRAIGHT);

  public static final FixedInflationSwapConvention EUR_FIXED_ZC_EU_EXT_CPI_CLEARED_30_360 =
      makeConvention(
          "EUR-FIXED-ZC-EU-EXT-CPI-CLEARED-30360",
          EUR,
          EUTA,
          PriceIndices.EU_EXT_CPI,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, EUTA),
          PriceIndexCalculationMethod.MONTHLY,
          2,
          THIRTY_E_360,
          CompoundingMethod.STRAIGHT);

  public static final FixedInflationSwapConvention JPY_FIXED_ZC_JP_CPI =
      makeConvention(
          "JPY-FIXED-ZC-JP-CPI",
          JPY,
          JPTO,
          PriceIndices.JP_CPI_EXF,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, JPTO),
          PriceIndexCalculationMethod.INTERPOLATED_JAPAN,
          2,
          ONE_ONE,
          CompoundingMethod.STRAIGHT);

  public static final FixedInflationSwapConvention JPY_FIXED_ZC_JP_CPI_30_360 =
      makeConvention(
          "JPY-FIXED-ZC-JP-CPI-30360",
          JPY,
          JPTO,
          PriceIndices.JP_CPI_EXF,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, JPTO),
          PriceIndexCalculationMethod.INTERPOLATED_JAPAN,
          2,
          THIRTY_E_360,
          CompoundingMethod.STRAIGHT);

  public static final FixedInflationSwapConvention JPY_FIXED_ZC_JP_CPI_INTERPOLATED_NAME =
      makeInterpolatedConvention(
          "JPY-FIXED-ZC-JP-CPI_INTERP",
          JPY,
          ExtendedHolidayCalendarIds.GBLO_JPTO,
          PriceIndices.JP_CPI_EXF,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, GBLO_JPTO),
          DayCounts.ACT_365F,
          CompoundingMethod.FLAT);

  public static final FixedInflationSwapConvention JPY_FIXED_ZC_JP_CPI_INTERPOLATED_NAME_30_360 =
      makeConvention(
          "JPY-FIXED-ZC-JP-CPI_INTERP-30360",
          JPY,
          ExtendedHolidayCalendarIds.GBLO_JPTO,
          PriceIndices.JP_CPI_EXF,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, GBLO_JPTO),
          PriceIndexCalculationMethod.INTERPOLATED,
          2,
          THIRTY_E_360,
          CompoundingMethod.FLAT);

  public static final FixedInflationSwapConvention USD_FIXED_ZC_US_CPI =
      makeInterpolatedConvention(
          "USD-FIXED-ZC-US-CPI",
          USD,
          GBLO_USNY,
          PriceIndices.US_CPI_U,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, GBLO_USNY),
          ONE_ONE,
          CompoundingMethod.STRAIGHT);

  public static final FixedInflationSwapConvention USD_FIXED_ZC_US_CPI_30_360 =
      makeConvention(
          "USD-FIXED-ZC-US-CPI-30360",
          USD,
          GBLO_USNY,
          PriceIndices.US_CPI_U,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, GBLO_USNY),
          PriceIndexCalculationMethod.INTERPOLATED,
          2,
          THIRTY_E_360,
          CompoundingMethod.STRAIGHT);

  public static final FixedInflationSwapConvention USD_FIXED_ZC_US_CPI_CLEARED =
      makeInterpolatedConvention(
          "USD-FIXED-ZC-US-CPI-CLEARED",
          USD,
          GBLO_USNY,
          PriceIndices.US_CPI_U,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, GBLO_USNY),
          ONE_ONE,
          CompoundingMethod.STRAIGHT);

  public static final FixedInflationSwapConvention USD_FIXED_ZC_US_CPI_CLEARED_30_360 =
      makeConvention(
          "USD-FIXED-ZC-US-CPI-CLEARED-30360",
          USD,
          GBLO_USNY,
          PriceIndices.US_CPI_U,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, GBLO_USNY),
          PriceIndexCalculationMethod.INTERPOLATED,
          2,
          THIRTY_E_360,
          CompoundingMethod.STRAIGHT);

  public static final FixedInflationSwapConvention EUR_FIXED_ZC_FR_CPI =
      makeConvention(
          "EUR-FIXED-ZC-FR-CPI",
          EUR,
          EUTA,
          PriceIndices.FR_EXT_CPI,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, FRPA),
          PriceIndexCalculationMethod.MONTHLY,
          2,
          ONE_ONE,
          CompoundingMethod.STRAIGHT);

  public static final FixedInflationSwapConvention EUR_FIXED_ZC_FR_CPI_30_360 =
      makeConvention(
          "EUR-FIXED-ZC-FR-CPI-30360",
          EUR,
          EUTA,
          PriceIndices.FR_EXT_CPI,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, FRPA),
          PriceIndexCalculationMethod.MONTHLY,
          2,
          THIRTY_E_360,
          CompoundingMethod.STRAIGHT);

  public static final FixedInflationSwapConvention EUR_FIXED_ZC_FR_CPI_CLEARED =
      makeClearedConvention(
          "EUR-FIXED-ZC-FR-CPI-CLEARED",
          EUR,
          EUTA,
          PriceIndices.FR_EXT_CPI,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, FRPA),
          2,
          CompoundingMethod.STRAIGHT);

  public static final FixedInflationSwapConvention EUR_FIXED_ZC_FR_CPI_CLEARED_30_360 =
      makeConvention(
          "EUR-FIXED-ZC-FR-CPI-CLEARED-30360",
          EUR,
          EUTA,
          PriceIndices.FR_EXT_CPI,
          BusinessDayAdjustment.of(MODIFIED_FOLLOWING, FRPA),
          PriceIndexCalculationMethod.MONTHLY,
          2,
          THIRTY_E_360,
          CompoundingMethod.STRAIGHT);

  // Create a zero-coupon fixed leg convention
  private static FixedRateSwapLegConvention fixedLegZcConvention(
      Currency ccy, HolidayCalendarId cal, DayCount dayCount, CompoundingMethod compoundingMethod) {
    return FixedRateSwapLegConvention.builder()
        .paymentFrequency(com.opengamma.strata.basics.schedule.Frequency.TERM)
        .accrualFrequency(Frequency.P12M)
        .accrualBusinessDayAdjustment(BusinessDayAdjustment.of(MODIFIED_FOLLOWING, cal))
        .startDateBusinessDayAdjustment(BusinessDayAdjustment.of(MODIFIED_FOLLOWING, cal))
        .endDateBusinessDayAdjustment(BusinessDayAdjustment.of(MODIFIED_FOLLOWING, cal))
        .compoundingMethod(compoundingMethod)
        .dayCount(dayCount)
        .currency(ccy)
        .build();
  }

  private static FixedInflationSwapConvention makeConvention(
      String name,
      Currency currency,
      HolidayCalendarId calendar,
      PriceIndex index,
      BusinessDayAdjustment businessDayAdjustment,
      PriceIndexCalculationMethod calculationMethod,
      int spotDateOffset,
      DayCount fixedLegDayCount,
      CompoundingMethod compoundingMethod) {
    return ImmutableFixedInflationSwapConvention.of(
        name,
        fixedLegZcConvention(currency, calendar, fixedLegDayCount, compoundingMethod),
        InflationRateSwapLegConvention.of(
            index,
            PriceIndexConstants.priceIndexLag(index),
            calculationMethod,
            businessDayAdjustment),
        DaysAdjustment.ofBusinessDays(spotDateOffset, calendar));
  }

  private static FixedInflationSwapConvention makeInterpolatedConvention(
      String name,
      Currency currency,
      HolidayCalendarId calendar,
      PriceIndex index,
      BusinessDayAdjustment businessDayAdjustment,
      DayCount dayCount,
      CompoundingMethod compoundingMethod) {
    return InterpolatedInflationSwapConvention.of(
        name,
        fixedLegZcConvention(currency, calendar, dayCount, compoundingMethod),
        InflationRateSwapLegConvention.of(
            index,
            PriceIndexConstants.priceIndexLag(index),
            PriceIndexCalculationMethod.INTERPOLATED,
            businessDayAdjustment),
        DaysAdjustment.ofBusinessDays(2, calendar));
  }

  private static FixedInflationSwapConvention makeClearedConvention(
      String name,
      Currency currency,
      HolidayCalendarId calendar,
      PriceIndex index,
      BusinessDayAdjustment businessDayAdjustment,
      int spotDateOffset,
      CompoundingMethod compoundingMethod) {
    return XplainFixedInflationSwapConvention.of(
        name,
        fixedLegZcConvention(currency, calendar, ONE_ONE, compoundingMethod),
        InflationRateSwapLegConvention.of(
            index,
            PriceIndexConstants.priceIndexLag(index),
            PriceIndexCalculationMethod.MONTHLY,
            businessDayAdjustment),
        DaysAdjustment.ofBusinessDays(spotDateOffset, calendar));
  }
}
