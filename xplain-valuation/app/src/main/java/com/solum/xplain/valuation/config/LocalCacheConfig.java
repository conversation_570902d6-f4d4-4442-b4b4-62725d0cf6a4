package com.solum.xplain.valuation.config;

import com.opengamma.strata.pricer.fxopt.InterpolatedStrikeSmileDeltaTermStructure;
import com.solum.xplain.shared.datagrid.LocalCacheReplica;
import com.solum.xplain.shared.datagrid.impl.redis.RedisJavaSerializationSupport;
import com.solum.xplain.valuation.messages.calibration.CalibrationCacheType;
import java.time.Duration;
import java.util.Arrays;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanFactoryPostProcessor;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class LocalCacheConfig {

  @Bean
  public RedisJavaSerializationSupport requiresJavaSerialization() {
    return new RedisJavaSerializationSupport(InterpolatedStrikeSmileDeltaTermStructure.class);
  }

  @Bean
  LocalCacheConfigPostProcessor localCacheConfigPostProcessor() {
    return new LocalCacheConfigPostProcessor();
  }

  static class LocalCacheConfigPostProcessor implements BeanFactoryPostProcessor {
    @Override
    public void postProcessBeanFactory(ConfigurableListableBeanFactory beanFactory)
        throws BeansException {
      Arrays.stream(CalibrationCacheType.values())
          .map(this::localCacheReplica)
          .forEach(c -> beanFactory.registerSingleton(c.name() + "LocalCacheReplica", c));
    }

    private LocalCacheReplica localCacheReplica(CalibrationCacheType cacheType) {
      return new LocalCacheReplica(
          cacheType.name(),
          LocalCacheReplica.DEFAULT_MAX_ENTRIES,
          LocalCacheReplica.DEFAULT_TTL,
          Duration.ofSeconds(10),
          false);
    }
  }
}
