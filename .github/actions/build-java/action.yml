name: 'Build Java'
description: 'Build Java'
inputs:
  github-token:
    description: "Github Token"
    required: true
  sonar-token:
    description: "Sonar Token"
    required: false
  module-name:
    description: "Module to build"
    required: false
  sonar-module:
    description: "Root module"
    required: false
  dockerhub-username:
    description: 'Docker Hub username'
    required: true
  dockerhub-token:
    description: 'Docker Hub personal access token'
    required: true
runs:
  using: "composite"
  steps:
    - name: Set up JDK 17
      uses: actions/setup-java@v4
      with:
        distribution: 'temurin'
        java-version: 17.0.7
    - name: Login to Docker Hub
      uses: docker/login-action@v3
      with:
        username: ${{ inputs.dockerhub-username }}
        password: ${{ inputs.dockerhub-token }}
    - name: Cache Docker images
      uses: actions/cache@v4
      with:
        path: ~/.docker
        key: docker-build-${{ runner.os }}-${{ inputs.module-name }}-${{ hashFiles('dependencies.gradle', '**/build.gradle') }}
        restore-keys: |
          docker-build-${{ runner.os }}-
    - name: Cache Gradle dependencies
      uses: actions/cache@v4
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
        restore-keys: |
          ${{ runner.os }}-gradle-
    - name: Build single module with Gradle
      if: ${{ inputs.module-name }}
      uses: burrunan/gradle-cache-action@v3
      with:
        arguments: clean :${{ inputs.module-name }}:buildNeeded --parallel -PskipITs
      env:
        GITHUB_TOKEN: ${{ inputs.github-token }}
    - name: Integration test single module with Gradle
      if: ${{ inputs.module-name }}
      uses: burrunan/gradle-cache-action@v3
      with:
        arguments: :${{ inputs.module-name }}:buildNeeded jacocoTestReport --no-parallel -PskipUnitTests
      env:
        GITHUB_TOKEN: ${{ inputs.github-token }}
    - name: Build whole project with Gradle
      if: ${{ inputs.module-name == '' }}
      uses: burrunan/gradle-cache-action@v3
      with:
        arguments: clean build --parallel -PskipITs
      env:
        GITHUB_TOKEN: ${{ inputs.github-token }}
    - name: Integration test whole project with Gradle
      if: ${{ inputs.module-name == '' }}
      uses: burrunan/gradle-cache-action@v3
      with:
        arguments: build jacocoTestReport --no-parallel -PskipUnitTests
      env:
        GITHUB_TOKEN: ${{ inputs.github-token }}
    - name: Analyze
      if: ${{ inputs.sonar-token && inputs.sonar-module }}
      run: ./gradlew :${{ inputs.sonar-module }}:sonar
      shell: bash
      env:
        GITHUB_TOKEN: ${{ inputs.github-token }}
        SONAR_TOKEN: ${{ inputs.sonar-token }}
    - name: Publish Test Report
      if: ${{ always() }}
      uses: mikepenz/action-junit-report@v5
      with:
        github_token: ${{ inputs.github-token }}
        report_paths: '**/build/test-results/*/TEST-*.xml'
        group_suite: true
