## System requirements
    + Windows/Linux/MacOS with at least 8GB RAM 
    + Java JDK 17 or later
    + <PERSON><PERSON>, <PERSON>er compose
    + Git
    + AWS CLI
    + IDEA 2019+

## Getting started with IDEA
   Creating project:

    File | New | Project from Version control | Git

    Note: Disable "Create separate module per source set" while integrating with gradle project.

   Installing Git Hooks (Required):

    After cloning the repository, run the following command to install git hooks:

    ```bash
    ./scripts/install-hooks.sh
    ```

    This installs a pre-commit hook that prevents committing license keys in application*.yml files.
   Enabling annotation processing:
           
    File | Settings | Build, Execution, Deplyment | Compiler | Annotation Processors => Enable annotation processing
   Enable build delegation to Gradle:
   
    File | Settings | Build, Execution, Deployment | Build Tools | Gradle | Runner => Delegate IDE build/run actions to gradle
   Setting up code formatter (http://gitlab.metasite.lan/metasite/dev-docs/blob/master/java-team/code-formatter/intellij-idea/mbs-code-style.xml):
   
    File | Settings | Editor | Code Style | Import => Scheme | Import scheme | Intellij IDEA Code style XML
    

## Starting third party services
    
Ensure you have `oauth2` host in your `/etc/hosts` file:

```sh
sudo echo "127.0.0.1 oauth2" | sudo tee -a /etc/hosts
```

To be able to run fully functional Xplain locally, following services (in addition to xplain-api)
 are required:
   ``` solum-xplain-ui, mongo, kafka, hazelcast, solum-xplain-valuation```
   
Running following docker-compose command will set everything up: 
   
```docker-compose --profile valuation up```

Running following docker-compose command will start everything up except for valuations service.

```docker-compose up```

Valuations service can be started externally using `solum-xplain-valuation` project.

NOTE: Make sure you are able to pull all images required by docker-compose. To be able to pull
images from AWS ECR you need to set up AWS CLI and install
`amazon-ecr credential helper` (if helper doesn't work
use ```$(aws ecr get-login --no-include-email)``` before pulling images).

### Starting third party services on Docker for Desktop

Network mode "host" is not available in Docker for Desktop.
Copy `docker-compose.override.yml.example` and rename to `docker-compose.override.yml` file.

## Running backend application

NOTE: BE application use libraries from github packages, for gradle to be able to authenticate
environment variable `GITHUB_TOKEN` should be set with personal github access token.

Create a token at https://github.com/settings/tokens with `read:packages` scope.

Add the following line to `~/.gradle/gradle.properties`:

```properties
GITHUB_TOKEN=YOUR_GITHUB
```

Using gradle:

```./gradlew clean bootRun```

Using IDEA:

``` -> ApiApplication.java -> Run 'ApiApplication' ```

Access FE:

[http://localhost:4200/xplain](http://localhost:4200/xplain)

### Running backend application with database dump from another environment

When running database dump locally couple of things must be noted. Make sure that local mock auth service (in `docker-compose.yml -> mock_oauth ->  JSON_CONFIG`):
- Mapped to the same roles/teams that dump has. Path `claims -> xplain/roles-teams`
- Use the same user (userId, sub field) if needed. Path `claims -> sub`

### Running backend application with Redis instead of Hazelcast

To run backend application with Redis data grid instead of Hazelcast, see the application properties in
`application-redis.yml` file (or run with the `redis` profile enabled). Also note the full set of Spring Data Redis
[https://docs.spring.io/spring-boot/appendix/application-properties/index.html#appendix.application-properties.data](configuration properties).
Xplain requires Redis 7.4 or later and the Jedis driver, version 5.2.0 or later, for HEXPIRE support.  

## API documentation

[http://localhost:8080/xplain/api/swagger-ui.html](http://localhost:8080/xplain/api/swagger-ui.html)

## Licensing
Licenses are created using: https://github.com/verhas/License3j

To create new license:
- Start REPL application https://github.com/verhas/license3jrepl
- Load private/public key from passwordstate
- Start new license `newLicense`
- Enable/Disable XVA `feature xva:BYTE=1/0`
- Add expiry date `feature expiryDate:DATE=SOME_DATE`
- Sign license `sign`
- Save license as base64 `saveLicense format=BASE64 file.txt`
- Set application variable to use new license `app.licenseKey`
