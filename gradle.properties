version=2.1.1
org.gradle.jvmargs=-Xmx4g -XX:MaxMetaspaceSize=1g -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8
org.gradle.caching=true
org.gradle.parallel=true
org.gradle.workers.max=4
#TODO: Remove after transition to 5.+ as compilation won't be supported at all
systemProp.sonar.gradle.skipCompile=true

# Uncomment the line below to diagnose things not getting cached. See https://docs.gradle.org/current/userguide/build_cache_debugging.html
# org.gradle.caching.debug=true
